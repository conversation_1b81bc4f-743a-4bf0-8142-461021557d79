package com.gw.multi.chat.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 字符串相似度工具类测试
 */
public class StringSimilarityUtilTest {

    @Test
    public void testCalculateSimilarity() {
        // 完全相同
        assertEquals(1.0, StringSimilarityUtil.calculateSimilarity("张三", "张三"), 0.001);
        
        // 完全不同
        assertTrue(StringSimilarityUtil.calculateSimilarity("张三", "李四") < 1.0);
        
        // 相似名称
        double similarity1 = StringSimilarityUtil.calculateSimilarity("张三", "张小三");
        assertTrue(similarity1 > 0.5);
        
        // 空字符串
        assertEquals(0.0, StringSimilarityUtil.calculateSimilarity("", "张三"), 0.001);
        assertEquals(0.0, StringSimilarityUtil.calculateSimilarity(null, "张三"), 0.001);
        
        // 大小写不敏感
        assertEquals(1.0, StringSimilarityUtil.calculateSimilarity("Alice", "alice"), 0.001);
        
        // 空格处理
        assertEquals(1.0, StringSimilarityUtil.calculateSimilarity(" 张三 ", "张三"), 0.001);
    }

    @Test
    public void testCalculateLCSSimilarity() {
        // 完全相同
        assertEquals(1.0, StringSimilarityUtil.calculateLCSSimilarity("张三", "张三"), 0.001);
        
        // 部分相同
        double similarity = StringSimilarityUtil.calculateLCSSimilarity("张三丰", "张小三");
        assertTrue(similarity > 0.0);
        
        // 空字符串
        assertEquals(0.0, StringSimilarityUtil.calculateLCSSimilarity("", "张三"), 0.001);
    }

    @Test
    public void testCalculateContainsSimilarity() {
        // 包含关系
        double similarity1 = StringSimilarityUtil.calculateContainsSimilarity("张三", "张三丰");
        assertTrue(similarity1 > 0.5);
        
        // 完全相同
        assertEquals(1.0, StringSimilarityUtil.calculateContainsSimilarity("张三", "张三"), 0.001);
        
        // 无包含关系
        double similarity2 = StringSimilarityUtil.calculateContainsSimilarity("张三", "李四");
        assertTrue(similarity2 < 0.5);
    }

    @Test
    public void testCalculateComprehensiveSimilarity() {
        // 完全相同
        assertEquals(1.0, StringSimilarityUtil.calculateComprehensiveSimilarity("张三", "张三"), 0.001);
        
        // 相似名称
        double similarity1 = StringSimilarityUtil.calculateComprehensiveSimilarity("张三", "张小三");
        assertTrue(similarity1 > 0.3);
        
        // 不相似名称
        double similarity2 = StringSimilarityUtil.calculateComprehensiveSimilarity("张三", "王五");
        assertTrue(similarity2 < 0.5);
        
        // 实际场景测试
        double similarity3 = StringSimilarityUtil.calculateComprehensiveSimilarity("周茵淑", "周茵");
        assertTrue(similarity3 > 0.6);
        
        double similarity4 = StringSimilarityUtil.calculateComprehensiveSimilarity("林夏", "林小夏");
        assertTrue(similarity4 > 0.5);
    }

    @Test
    public void testRealWorldScenarios() {
        // 测试实际可能出现的角色名称匹配场景

        // 简称匹配
        double sim1 = StringSimilarityUtil.calculateComprehensiveSimilarity("周茵淑", "茵淑");
        System.out.println("周茵淑 vs 茵淑: " + sim1);
        assertTrue(sim1 > 0.4);

        double sim2 = StringSimilarityUtil.calculateComprehensiveSimilarity("林夏", "小夏");
        System.out.println("林夏 vs 小夏: " + sim2);
        assertTrue(sim2 > 0.2);

        // 错别字匹配
        double sim3 = StringSimilarityUtil.calculateComprehensiveSimilarity("张三", "张山");
        System.out.println("张三 vs 张山: " + sim3);
        assertTrue(sim3 >= 0.5);

        double sim4 = StringSimilarityUtil.calculateComprehensiveSimilarity("李四", "李思");
        System.out.println("李四 vs 李思: " + sim4);
        assertTrue(sim4 >= 0.4);

        // 添加修饰词
        double sim5 = StringSimilarityUtil.calculateComprehensiveSimilarity("王五", "小王五");
        System.out.println("王五 vs 小王五: " + sim5);
        assertTrue(sim5 > 0.4);

        double sim6 = StringSimilarityUtil.calculateComprehensiveSimilarity("赵六", "赵老六");
        System.out.println("赵六 vs 赵老六: " + sim6);
        assertTrue(sim6 > 0.4);

        // 完全不相关的名称
        double sim7 = StringSimilarityUtil.calculateComprehensiveSimilarity("张三", "李四");
        System.out.println("张三 vs 李四: " + sim7);
        assertTrue(sim7 < 0.4);

        double sim8 = StringSimilarityUtil.calculateComprehensiveSimilarity("周茵淑", "王小明");
        System.out.println("周茵淑 vs 王小明: " + sim8);
        assertTrue(sim8 < 0.4);
    }

    @Test
    public void testEdgeCases() {
        // 单字符
        assertTrue(StringSimilarityUtil.calculateComprehensiveSimilarity("A", "B") < 0.5);
        assertEquals(1.0, StringSimilarityUtil.calculateComprehensiveSimilarity("A", "A"), 0.001);
        
        // 长度差异很大
        double similarity = StringSimilarityUtil.calculateComprehensiveSimilarity("张", "张三丰李四王五");
        assertTrue(similarity > 0.0 && similarity < 0.5);
        
        // 特殊字符
        assertTrue(StringSimilarityUtil.calculateComprehensiveSimilarity("张@三", "张三") > 0.5);
        assertTrue(StringSimilarityUtil.calculateComprehensiveSimilarity("李#四", "李四") > 0.5);
    }
}
