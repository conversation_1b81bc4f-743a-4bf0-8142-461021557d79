package com.gw.multi.chat.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.security.WxMaMsgSecCheckCheckRequest;
import cn.binarywang.wx.miniapp.bean.security.WxMaMsgSecCheckCheckResponse;
import com.alibaba.fastjson2.JSON;
import com.gw.common.agent.service.SensitiveWordDetectProxyService;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.multi.chat.config.CacheProperties;
import com.gw.multi.chat.service.SecurityCheckService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;

/**
 * 安全检查服务，负责对内容进行异步安全检查
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class SecurityCheckServiceImpl implements SecurityCheckService {

    private final WxMaService wxMaService;
    private final UserProxyService userProxyService;
    private final CacheProperties cacheProperties;
    private final SensitiveWordDetectProxyService sensitiveWordDetectProxyService;
    @Override
    public boolean performSecurityCheck(String username, String content) {
        try {
            UserBaseContentVo user = userProxyService.findByUsername(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY), username);
            WxMaMsgSecCheckCheckResponse response;
            if (user.getWxOpenId() != null) {
                response = wxMaService.getSecurityService()
                        .checkMessage(WxMaMsgSecCheckCheckRequest.builder().content(content).openid(user.getWxOpenId()).build());
            } else {
                response = wxMaService.getSecurityService()
                        .checkMessage(WxMaMsgSecCheckCheckRequest.builder().content(content).build());
            }
            log.info("安全检查结果 username: {}, content: {}, 结果: {} result{}", username, content, JSON.toJSONString(response), response.getResult());
            var ret =  response.getErrcode().equals(0);
            if(!ret){
                return false;
            }
            return sensitiveWordDetectProxyService.checkContent(content);
        } catch (WxErrorException e) {
            log.error(e.getMessage());
            return false;
        }
    }
}