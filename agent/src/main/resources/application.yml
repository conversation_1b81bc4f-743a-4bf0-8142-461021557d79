server:
  port: 8006
  tomcat:
    connection-timeout: 20000
    uri-encoding: UTF-8
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
      force-request: true
      force-response: true
spring:
  profiles:
    include:
      - tencent-cos
      - async
      - sensitive-word
  application:
    name: agent-service
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  data:
    redis:
      database: 0
      port: 6379
      host: localhost
      timeout: 10000
      client-type: lettuce
      lettuce:
        pool:
          # 连接池最大连接数（使用负值表示没有限制） 默认 8
          max-active: 20
          # 连接池中的最大空闲连接 默认 8
          max-idle: 10
          # 连接池中的最小空闲连接 默认 0
          min-idle: 3
          # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
          max-wait: 1s
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **************************************************************
    username: postgres
    password: root
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848

mybatis-plus:
  ddl:
    application-runner-enabled: false
  mapper-locations: classpath*:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  global-config:
    db-config:
      id-type: auto # 或者 assign_id, input, uuid, id_worker, snowflake 等其他策略
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # ??SQL??
    # 确保所有类型处理器能被自动扫描并注册
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    # 映射下划线到驼峰命名
    map-underscore-to-camel-case: true
    # 开启自动映射
    auto-mapping-behavior: full
    # 返回结果支持自动映射
    auto-mapping-unknown-column-behavior: warning
  # 指定TypeHandler所在的包，使其能被自动扫描
  type-handlers-package: com.gw.agent.handler
pagehelper:
  helper-dialect: postgresql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
logging:
  config: classpath:log4j2.xml
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /api/v1/agent/swagger-ui.html
  paths-to-match:
coze:
  workflow:
    to-image-number: 4
    ai-prompt-id: 7502026469570560035
    text-to-image-id: 7503882761515139106
    text-image-prompt-optimize-id: 7506187324813754422
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
  compression:
    request:
      enabled: true
    response:
      enabled: true
cache:
  prefix: lingxi
  configs:
    - name: agentBase
      expireAfterWrite: 1h
    - name: agentTag
      expireAfterWrite: 720h
    - name: agentType
      expireAfterWrite: 720h
    - name: agent
      expireAfterWrite: 1h
    - name: agentAll
      expireAfterWrite: 1h
    - name: coze
      expireAfterWrite: 720h
    - name: agentPageCache
      expireAfterWrite: 30m
    - name: agentCnt
      expireAfterWrite: 24h
    - name: agentLikeCnt
      expireAfterWrite: 24h
    - name: agentIsLike
      expireAfterWrite: 24h
    - name: agentFavoriteCnt
      expireAfterWrite: 24h
    - name: agentIsFavorite
      expireAfterWrite: 24h
    - name: agentUsageRecCnt
      expireAfterWrite: 24h
    - name: agentUsageIds
      expireAfterWrite: 24h
    - name: agentBatchLike
      expireAfterWrite: 10m
    - name: agentCommentCnt
      expireAfterWrite: 24h
    - name: agentCommentIds
      expireAfterWrite: 24h
    - name: agentUsrInterest
      expireAfterWrite: 24h
    - name: agentSetting
      expireAfterWrite: 1h
    - name: userPreference
      expireAfterWrite: 24h
    - name: agentStoryFavoriteCnt
      expireAfterWrite: 24h
    - name: agentStoryUsrInterest
      expireAfterWrite: 24h
    - name: agentStoryLikeCnt
      expireAfterWrite: 24h
    - name: agentStoryBatchLike
      expireAfterWrite: 24h
    - name: agentStoryIsLike
      expireAfterWrite: 24h
    - name: agentStoryTag
      expireAfterWrite: 24h
    - name: agentStoryType
      expireAfterWrite: 24h
    - name: agentStoryBase
      expireAfterWrite: 720h
# WeChat Mini Program configuration
wx:
  miniapp:
    appid: ${WX_MINIAPP_APPID:wxe96c29e240e54ff0}
    secret: ${WX_MINIAPP_SECRET:7b9a54bc3bf8070c34be5cb184602561}
    # 消息推送验证Token，用于验证请求来源
    callback-token: ${WX_MINIAPP_CALLBACK_TOKEN:fafdafdsa}
    access-token-url: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
    msg-sec-check-url: https://api.weixin.qq.com/wxa/msg_sec_check
    default-openid: test-openid # 测试用的OpenID，实际使用时应该传入真实用户的OpenID
# 缩略图配置
agent:
  thumbnail:
    width: 576
    height: 1024
    quality: 0.7

# 异步任务线程池配置已移至 application-async.yml
host:
  url: https://www.guanwei-ai.com
