package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * 敏感词状态更新参数
 */
@Data
@Schema(description = "敏感词状态更新参数")
public class SensitiveWordStatusUpdateDTO {
    
    @Schema(description = "敏感词ID")
    @NotNull(message = "敏感词ID不能为空")
    @Positive(message = "敏感词ID必须为正数")
    private Long id;
    
    @Schema(description = "状态：0-禁用，1-启用")
    @NotNull(message = "状态不能为空")
    private Integer status;
}
