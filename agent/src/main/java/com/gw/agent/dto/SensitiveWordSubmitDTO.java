package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * 敏感词提交参数
 */
@Data
@Schema(description = "敏感词提交参数")
public class SensitiveWordSubmitDTO {
    
    @Schema(description = "敏感词内容")
    @NotBlank(message = "敏感词内容不能为空")
    private String word;
    
    @Schema(description = "分类ID")
    @NotNull(message = "分类ID不能为空")
    @Positive(message = "分类ID必须为正数")
    private Long categoryId;
    
    @Schema(description = "敏感词级别：1-低级，2-中级，3-高级，4-严重")
    @NotNull(message = "敏感词级别不能为空")
    @Positive(message = "敏感词级别必须为正数")
    private Integer level;
    
//    @Schema(description = "处理动作：1-拦截，2-替换，3-警告")
//    @NotNull(message = "处理动作不能为空")
//    @Positive(message = "处理动作必须为正数")
    private Integer action = 1;
    
//    @Schema(description = "替换内容（当action为2时使用）")
    private String replacement="";
    
    @Schema(description = "状态：0-禁用，1-启用")
    @NotNull(message = "状态不能为空")
    private Integer status;

}
