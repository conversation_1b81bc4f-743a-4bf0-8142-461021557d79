package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * Sensitive word category status update DTO
 */
@Data
@Schema(description = "Sensitive word category status update DTO")
public class SensitiveWordCategoryStatusUpdateDTO {

    @Schema(description = "Category ID")
    @NotNull(message = "Category ID cannot be null")
    @Positive(message = "Category ID must be positive")
    private Long id;

    @Schema(description = "Status: 0-disabled, 1-enabled")
    @NotNull(message = "Status cannot be null")
    private Integer status;
}
