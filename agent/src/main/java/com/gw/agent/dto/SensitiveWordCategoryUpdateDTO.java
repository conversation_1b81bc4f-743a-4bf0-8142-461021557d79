package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * Sensitive word category update DTO
 */
@Data
@Schema(description = "Sensitive word category update DTO")
public class SensitiveWordCategoryUpdateDTO {

    @Schema(description = "Category ID")
    @NotNull(message = "Category ID cannot be null")
    @Positive(message = "Category ID must be positive")
    private Long id;

    @Schema(description = "Category name")
    @NotBlank(message = "Category name cannot be blank")
    private String name;

    @Schema(description = "Category description")
    private String description;

    @Schema(description = "Status: 0-disabled, 1-enabled")
    @NotNull(message = "Status cannot be null")
    private Integer status;

    @Schema(description = "Sort sequence")
    private Integer sequence = 0;
}
