package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 敏感词导出参数
 */
@Data
@Schema(description = "敏感词导出参数")
public class SensitiveWordExportDTO {
    
    @Schema(description = "分类ID列表，为空则导出所有分类")
    private List<Long> categoryIds;
    
    @Schema(description = "敏感词级别列表，为空则导出所有级别")
    private List<Integer> levels;
    
    @Schema(description = "处理动作列表，为空则导出所有动作")
    private List<Integer> actions;
    
    @Schema(description = "状态：0-禁用，1-启用，为空则导出所有状态")
    private Integer status;
    
    @Schema(description = "导出格式：1-仅敏感词，2-包含详细信息")
    private Integer format = 1;
    
    @Schema(description = "文件名前缀")
    private String fileNamePrefix = "sensitive_words";
}
