package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 敏感词导入参数
 */
@Data
@Schema(description = "敏感词导入参数")
public class SensitiveWordImportDTO {
    
    @Schema(description = "敏感词文件")
    @NotNull(message = "敏感词文件不能为空")
    private MultipartFile file;
    
    @Schema(description = "分类ID")
    @NotNull(message = "分类ID不能为空")
    @Positive(message = "分类ID必须为正数")
    private Long categoryId;
    
    @Schema(description = "敏感词级别：1-低级，2-中级，3-高级，4-严重")
    @NotNull(message = "敏感词级别不能为空")
    @Positive(message = "敏感词级别必须为正数")
    private Integer level = 1;
    
    @Schema(description = "处理动作：1-拦截，2-替换，3-警告")
    @NotNull(message = "处理动作不能为空")
    @Positive(message = "处理动作必须为正数")
    private Integer action = 1;
    
    @Schema(description = "替换内容（当action为2时使用）")
    private String replacement;
    
    @Schema(description = "状态：0-禁用，1-启用")
    @NotNull(message = "状态不能为空")
    private Integer status = 1;
    
    @Schema(description = "是否覆盖已存在的敏感词")
    private Boolean overwrite = false;
}
