package com.gw.agent.service;

import com.gw.agent.constant.AgentCacheConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 手动缓存清除服务
 * 用于手动清除特定的缓存项
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class CacheManualClearService {
    
    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;
    
    // 缓存名称常量
    private final String CacheValueCnt = "agentStoryCommentCnt";
    private final String CacheStoryIds = "agentStoryCommentIds";
    
    /**
     * 手动清除指定的缓存项
     * 
     * @param storyId 故事ID
     * @param username 用户名
     */
    public void clearSpecificCaches(Long storyId, String username) {
        log.info("开始手动清除缓存 - 故事ID: {}, 用户名: {}", storyId, username);
        
        try {
            // 1. 清除 CacheValueCnt 缓存中的用户相关项
            clearCacheValueCntUserCache(username);
            
            // 2. 清除 AgentCacheConstant.AGENT_CACHE 缓存中的故事相关项
            clearAgentCacheStoryCache(storyId);
            
            // 3. 清除 CacheStoryIds 缓存中的用户评论相关项
            clearCacheStoryIdsCommentCache(username);
            
            // 4. 清除 AgentCacheConstant.AGENT_PAGE_CACHE 所有项
            clearAgentPageCache();
            
            // 5. 清除 CacheValueCnt 缓存中的故事相关项
            clearCacheValueCntStoryCache(storyId);
            
            log.info("手动清除缓存完成 - 故事ID: {}, 用户名: {}", storyId, username);
            
        } catch (Exception e) {
            log.error("手动清除缓存失败 - 故事ID: {}, 用户名: {}, 错误: {}", storyId, username, e.getMessage(), e);
            throw new RuntimeException("清除缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除 CacheValueCnt 缓存中的用户相关项
     * 对应: @CacheEvict(value = CacheValueCnt, key = "'user:' + #entity.username")
     */
    private void clearCacheValueCntUserCache(String username) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(CacheValueCnt);
            if (cache != null) {
                String cacheKey = "user:" + username;
                cache.evict(cacheKey);
                log.debug("清除缓存: {} - key: {}", CacheValueCnt, cacheKey);
            }
        } catch (Exception e) {
            log.warn("清除用户缓存失败 - 用户名: {}, 错误: {}", username, e.getMessage());
        }
    }
    
    /**
     * 清除 AgentCacheConstant.AGENT_CACHE 缓存中的故事相关项
     * 对应: @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'story:' + #storyId")
     */
    private void clearAgentCacheStoryCache(Long storyId) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(AgentCacheConstant.AGENT_CACHE);
            if (cache != null) {
                String cacheKey = "story:" + storyId;
                cache.evict(cacheKey);
                log.debug("清除缓存: {} - key: {}", AgentCacheConstant.AGENT_CACHE, cacheKey);
            }
        } catch (Exception e) {
            log.warn("清除故事缓存失败 - 故事ID: {}, 错误: {}", storyId, e.getMessage());
        }
    }
    
    /**
     * 清除 CacheStoryIds 缓存中的用户评论相关项
     * 对应: @CacheEvict(value = CacheStoryIds, key = "'comment:' + #entity.username")
     */
    private void clearCacheStoryIdsCommentCache(String username) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(CacheStoryIds);
            if (cache != null) {
                String cacheKey = "comment:" + username;
                cache.evict(cacheKey);
                log.debug("清除缓存: {} - key: {}", CacheStoryIds, cacheKey);
            }
        } catch (Exception e) {
            log.warn("清除用户评论缓存失败 - 用户名: {}, 错误: {}", username, e.getMessage());
        }
    }
    
    /**
     * 清除 AgentCacheConstant.AGENT_PAGE_CACHE 所有项
     * 对应: @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true)
     */
    private void clearAgentPageCache() {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(AgentCacheConstant.AGENT_PAGE_CACHE);
            if (cache != null) {
                cache.clear();
                log.debug("清除缓存: {} - 所有项", AgentCacheConstant.AGENT_PAGE_CACHE);
            }
        } catch (Exception e) {
            log.warn("清除页面缓存失败 - 错误: {}", e.getMessage());
        }
    }
    
    /**
     * 清除 CacheValueCnt 缓存中的故事相关项
     * 对应: @CacheEvict(value = CacheValueCnt, key = "'story:' + #storyId")
     */
    private void clearCacheValueCntStoryCache(Long storyId) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(CacheValueCnt);
            if (cache != null) {
                String cacheKey = "story:" + storyId;
                cache.evict(cacheKey);
                log.debug("清除缓存: {} - key: {}", CacheValueCnt, cacheKey);
            }
        } catch (Exception e) {
            log.warn("清除故事计数缓存失败 - 故事ID: {}, 错误: {}", storyId, e.getMessage());
        }
    }
    
    /**
     * 清除所有相关缓存（危险操作，谨慎使用）
     */
    public void clearAllRelatedCaches() {
        log.warn("开始清除所有相关缓存 - 这是一个危险操作");
        
        try {
            // 清除所有相关缓存
            clearCacheByName(CacheValueCnt);
            clearCacheByName(AgentCacheConstant.AGENT_CACHE);
            clearCacheByName(CacheStoryIds);
            clearCacheByName(AgentCacheConstant.AGENT_PAGE_CACHE);
            
            log.warn("清除所有相关缓存完成");
            
        } catch (Exception e) {
            log.error("清除所有相关缓存失败 - 错误: {}", e.getMessage(), e);
            throw new RuntimeException("清除所有缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据缓存名称清除整个缓存
     */
    private void clearCacheByName(String cacheName) {
        try {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                log.debug("清除整个缓存: {}", cacheName);
            }
        } catch (Exception e) {
            log.warn("清除缓存失败 - 缓存名: {}, 错误: {}", cacheName, e.getMessage());
        }
    }
    
    /**
     * 获取缓存状态信息
     */
    public String getCacheStatus() {
        StringBuilder status = new StringBuilder();
        status.append("缓存状态信息:\n");
        
        String[] cacheNames = {CacheValueCnt, AgentCacheConstant.AGENT_CACHE, 
                              CacheStoryIds, AgentCacheConstant.AGENT_PAGE_CACHE};
        
        for (String cacheName : cacheNames) {
            try {
                org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    status.append(String.format("- %s: 存在\n", cacheName));
                } else {
                    status.append(String.format("- %s: 不存在\n", cacheName));
                }
            } catch (Exception e) {
                status.append(String.format("- %s: 检查失败 - %s\n", cacheName, e.getMessage()));
            }
        }
        
        return status.toString();
    }
}
