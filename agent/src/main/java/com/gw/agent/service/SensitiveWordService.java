package com.gw.agent.service;

import com.github.pagehelper.PageInfo;
import com.gw.agent.dto.SensitiveWordExportDTO;
import com.gw.agent.dto.SensitiveWordQueryDTO;
import com.gw.agent.entity.SensitiveWordEntity;
import com.gw.agent.vo.SensitiveWordImportResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 敏感词服务接口
 */
public interface SensitiveWordService {
    
    /**
     * 创建敏感词
     */
    void insert(SensitiveWordEntity entity);
    
    /**
     * 更新敏感词
     */
    void update(SensitiveWordEntity entity);
    
    /**
     * 删除敏感词
     */
    void delete(Long id);
    
    /**
     * 根据ID查询敏感词
     */
    SensitiveWordEntity findById(Long id);
    
    /**
     * 根据词内容查询敏感词
     */
    SensitiveWordEntity findByWord(String word);
    
    /**
     * 查询所有启用的敏感词
     */
    List<SensitiveWordEntity> findAllEnabled();
    
    /**
     * 查询所有敏感词
     */
    List<SensitiveWordEntity> findAll();
    
    /**
     * 根据分类ID查询敏感词
     */
    List<SensitiveWordEntity> findByCategoryId(Long categoryId);
    
    /**
     * 分页查询敏感词
     */
    PageInfo<SensitiveWordEntity> page(int pageNum, int pageSize, SensitiveWordQueryDTO query);
    
    /**
     * 更新使用次数
     */
    void updateUseCount(Long id, Integer increment);
    
    /**
     * 批量导入敏感词
     */
    void batchImport(List<SensitiveWordEntity> words);
    
    /**
     * 批量删除敏感词
     */
    void batchDelete(List<Long> ids);
    
    /**
     * 启用/禁用敏感词
     */
    void updateStatus(Long id, Integer status);
    
    /**
     * 清除敏感词缓存
     */
    void clearCache();

    /**
     * 从文件导入敏感词
     *
     * @param file 敏感词文件
     * @param categoryId 分类ID
     * @param level 敏感词级别
     * @param action 处理动作
     * @param replacement 替换内容
     * @param status 状态
     * @param overwrite 是否覆盖已存在的敏感词
     * @return 导入结果
     */
    SensitiveWordImportResultVO importFromFile(MultipartFile file, Long categoryId, Integer level,
                                               Integer action, String replacement, Integer status, Boolean overwrite);

    /**
     * 导出敏感词到文件
     *
     * @param exportDTO 导出参数
     * @return 文件内容字节数组
     */
    byte[] exportToFile(SensitiveWordExportDTO exportDTO);

    /**
     * 根据导出条件查询敏感词
     *
     * @param exportDTO 导出参数
     * @return 敏感词列表
     */
    List<SensitiveWordEntity> findForExport(SensitiveWordExportDTO exportDTO);
}
