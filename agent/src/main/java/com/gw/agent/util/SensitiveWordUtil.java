package com.gw.agent.util;

import com.gw.agent.service.SensitiveWordDetectionService;
import com.gw.common.agent.dto.SensitiveWordCheckResultVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * 敏感词检测工具类
 */
@Component
@Log4j2
public class SensitiveWordUtil {
    
    private static SensitiveWordDetectionService detectionService;
    
    @Autowired
    public void setDetectionService(SensitiveWordDetectionService detectionService) {
        SensitiveWordUtil.detectionService = detectionService;
    }
    
    /**
     * 检测文本是否包含敏感词
     * 
     * @param content 待检测文本
     * @return 是否包含敏感词
     */
    public static boolean containsSensitiveWord(String content) {
        if (!StringUtils.hasText(content) || detectionService == null) {
            return false;
        }
        
        try {
            return detectionService.containsSensitiveWord(content);
        } catch (Exception e) {
            log.error("敏感词检测异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取文本中的敏感词列表
     * 
     * @param content 待检测文本
     * @return 敏感词列表
     */
    public static List<String> findSensitiveWords(String content) {
        if (!StringUtils.hasText(content) || detectionService == null) {
            return Collections.emptyList();
        }
        
        try {
            return detectionService.findSensitiveWords(content);
        } catch (Exception e) {
            log.error("敏感词查找异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    
    /**
     * 检测文本中的敏感词（详细模式）
     * 
     * @param content 待检测文本
     * @return 检测结果
     */
    public static SensitiveWordCheckResultVO checkText(String content) {
        if (!StringUtils.hasText(content) || detectionService == null) {
            return new SensitiveWordCheckResultVO(false, Collections.emptyList(), content, content, Collections.emptyList());
        }
        
        try {
            return detectionService.checkText(content, true);
        } catch (Exception e) {
            log.error("敏感词检测异常: {}", e.getMessage(), e);
            return new SensitiveWordCheckResultVO(false, Collections.emptyList(), content, content, Collections.emptyList());
        }
    }
    
    /**
     * 验证文本是否通过敏感词检测
     * 
     * @param content 待检测文本
     * @return 是否通过检测（true表示无敏感词，false表示有敏感词）
     */
    public static boolean isTextValid(String content) {
        return !containsSensitiveWord(content);
    }

}
