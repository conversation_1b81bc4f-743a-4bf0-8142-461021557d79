package com.gw.agent.controller;

import com.github.pagehelper.PageInfo;
import com.gw.agent.config.CacheProperties;
import com.gw.agent.constant.AgentFileConstants;
import com.gw.agent.dto.AgentStoryCommentAdminQueryDTO;
import com.gw.agent.dto.AgentStoryCommentCreateDTO;
import com.gw.agent.dto.AgentStoryCommentLikeDTO;
import com.gw.agent.dto.AgentStoryCommentQueryDTO;
import com.gw.agent.entity.AgentStoryCommentEntity;
import com.gw.agent.entity.AgentStoryEntity;
import com.gw.agent.exception.SecurityCheckException;
import com.gw.agent.service.*;
import com.gw.agent.vo.AgentStoryCommentVO;
import com.gw.common.agent.dto.SensitiveWordCheckResultVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.util.UploadFileUtil;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import com.gw.common.vo.UploadFileVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;
import static com.gw.common.exception.BusinessExceptionCode.VIOLATION_CODE;

/**
 * 智能体剧情评论控制器
 */
@RestController
@RequestMapping("/api/v1/agent/story/comments")
@RequiredArgsConstructor
@Tag(name = "智能体剧情评论", description = "智能体剧情评论相关API")
@Log4j2
public class AgentStoryCommentController {
    private final CacheProperties cacheProperties;
    private final UserProxyService userProxyService;
    private final AgentStoryCommentService agentStoryCommentService;
    private final AgentStoryCommentLikeService commentLikeService;
    private final AgentStoryService agentStoryService;
    private final SecurityCheckService securityCheckService;
    private final SensitiveWordDetectionService sensitiveWordDetectionService;
    /**
     * 添加评论
     */
    @Operation(summary = "添加评论", description = "添加智能体剧情评论或回复评论")
    @PostMapping("/add")
    public ResponseResult<AgentStoryCommentVO> addComment(@RequestBody @Valid AgentStoryCommentCreateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        try {
            SensitiveWordCheckResultVO result = sensitiveWordDetectionService.checkText(req.getContent());
            if(result.getHasSensitiveWord()){
                return ResponseResult.failure(VIOLATION_CODE, "评论内容违规");
            }
            securityCheckService.checkTextWithTencentCOS("剧情评论", req.getContent());
        } catch (SecurityCheckException e) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "内容违规");
        }catch (Exception e) {
            log.error("内容审核失败: {}", e.getMessage(), e);
            return ResponseResult.failure(FAIL_CODE.getCode(), "内容审核失败");
        }
        AgentStoryEntity story = agentStoryService.findStoryById(req.getStoryId());

        AgentStoryCommentEntity entity = agentStoryCommentService.addCommentVO(
                story.getId(),
                username,
                req.getContent(),
                req.getParentId(),
                req.getReplyToUsername(),
                req.getImages(),
                req.getMessages());
        Set<String> usernames = Stream.of(entity.getUsername(), entity.getReplyToUsername())
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<String, UserBaseContentVo> usrMap =findAllUserMap(new ArrayList<>(usernames));
        entity.setImageList(req.getImages());
        return ResponseResult.success(convertToVO(entity,usrMap,new HashMap<>()));
    }

    /**
     * 删除评论
     */
    @Operation(summary = "删除评论", description = "删除自己发布的评论")
    @PostMapping("/delete")
    public ResponseResult<Boolean> deleteComment(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        AgentStoryCommentEntity entity = agentStoryCommentService.findById(req.getId());
        boolean result = agentStoryCommentService.deleteComment(entity.getStoryId(), req.getId(), username);
        return ResponseResult.success(result);
    }
    @Operation(summary = "后台删除评论", description = "后台删除评论")
    @PostMapping("/admin/delete")
    public ResponseResult<Boolean> adminDeleteComment(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        AgentStoryCommentEntity entity = agentStoryCommentService.findById(req.getId());
        boolean result = agentStoryCommentService.adminDeleteComment(entity.getStoryId(), req.getId());
        return ResponseResult.success(result);
    }
    private Map<String, UserBaseContentVo> findAllUserMap(List< String> usernames){
        String cacheKey = cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY);
        return userProxyService.findAllUserMapByUsernames(cacheKey,usernames);
    }
    private AgentStoryCommentVO convertToVO(AgentStoryCommentEntity entity,Map<String, UserBaseContentVo> usrMap,Map<Long, AgentStoryEntity> storyMap) {
        if (entity == null) {
            return null;
        }
        String nickname,avatarUrl,replyToNickname,replyToAvatarUrl;
        if(usrMap.containsKey(entity.getUsername())){
            nickname = usrMap.get(entity.getUsername()).getNickname();
            avatarUrl = usrMap.get(entity.getUsername()).getAvatar();
        }else{
            nickname = entity.getNickname();
            avatarUrl = entity.getAvatarUrl();
        }
        if(entity.getReplyToUsername() != null && !entity.getReplyToUsername().isEmpty()){
            if(usrMap.containsKey(entity.getReplyToUsername())){
                replyToNickname = usrMap.get(entity.getReplyToUsername()).getNickname();
                replyToAvatarUrl = usrMap.get(entity.getReplyToUsername()).getAvatar();
            }else{
                replyToNickname = entity.getReplyToUsername();
                replyToAvatarUrl = "";
            }
        }else{
            replyToNickname = "";
            replyToAvatarUrl = "";
        }

        AgentStoryCommentVO vo = AgentStoryCommentVO.builder()
                .id(entity.getId())
                .storyId(entity.getStoryId())
                .username(entity.getUsername())
                .nickname(nickname)
                .avatarUrl(avatarUrl)
                .content(entity.getContent())
                .parentId(entity.getParentId())
                .replyToUsername(entity.getReplyToUsername())
                .replyToNickname(replyToNickname)
                .replyToAvatarUrl(replyToAvatarUrl)
                .status(entity.getStatus())
                .likeCount(entity.getLikeCount())
                .isLiked(entity.getIsLiked())
                .replyCount(entity.getReplyCount())
                .imageList(entity.getImageList() != null?entity.getImageList():new ArrayList<>())
                .messageList(entity.getMessageList() != null?entity.getMessageList():new ArrayList<>())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .build();



        // 转换最新子评论
        if (entity.getLatestChild() != null) {
            vo.setLatestChild(convertToVO(entity.getLatestChild(),usrMap,new HashMap<>()));
        }
        if(storyMap != null && storyMap.containsKey(entity.getStoryId())){
            AgentStoryEntity story = storyMap.get(entity.getStoryId());
            vo.setStoryName(story.getName());
        }else{
            vo.setStoryName("");
        }
        return vo;
    }
    /**
     * 分页获取剧情评论列表
     */
    @Operation(summary = "分页获取剧情评论列表", description = "分页获取指定剧情的评论列表")
    @PostMapping("/page")
    public ResponseResult<PageBaseContentVo<AgentStoryCommentVO>> getStoryComments(
            @RequestBody @Valid PageBaseRequest<AgentStoryCommentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        log.info("{}分页获取剧情评论", username);

        AgentStoryCommentQueryDTO filter = params.getFilter();
        if (filter == null || filter.getStoryId() == null) {
            throw new IllegalArgumentException("剧情ID不能为空");
        }

        // 将parentId为0的情况转换为null，表示查询顶级评论
        Long parentId = filter.getParentId();
        if (parentId != null && parentId == 0) {
            parentId = null;
        }

        PageInfo<AgentStoryCommentEntity> page = agentStoryCommentService.pageStoryComments(
                params.getCurrent(), params.getPageSize(), filter.getStoryId(), parentId, username);
        if(page.getList() != null){
            Set<String> usernames = page.getList().stream()
                    .flatMap(history -> Stream.of(history.getUsername(), history.getReplyToUsername()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Map<String, UserBaseContentVo> usrMap =findAllUserMap(new ArrayList<>(usernames));
            List<AgentStoryCommentVO> vos = page.getList().stream()
                    .map(entity -> convertToVO(entity,usrMap,new HashMap<>()))
                    .toList();
            PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
            return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
        }
        PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.success(new PageBaseContentVo<>(new ArrayList<>(), pagination));
    }
    @Operation(summary = "后台分页获取剧情评论列表", description = "后台分页获取指定剧情的评论列表")
    @PostMapping("/admin/page")
    public ResponseResult<PageBaseContentVo<AgentStoryCommentVO>> adminGetStoryComments(
            @RequestBody @Valid PageBaseRequest<AgentStoryCommentAdminQueryDTO> params) {

        log.info("后台分页获取剧情评论列表");

        AgentStoryCommentAdminQueryDTO filter = params.getFilter();
        if (filter == null) {
            filter = new AgentStoryCommentAdminQueryDTO();
        }

        // 根据storyName模糊查询storyId列表
        List<Long> storyIds = null;
        if (filter.getStoryName() != null && !filter.getStoryName().trim().isEmpty()) {
            storyIds = agentStoryService.findStoryIdsByNameLike(filter.getStoryName());
            if (storyIds.isEmpty()) {
                // 如果根据名称没有找到任何剧情，返回空结果
                PaginationVo pagination = new PaginationVo(0L, params.getCurrent(), params.getPageSize());
                return ResponseResult.success(new PageBaseContentVo<>(new ArrayList<>(), pagination));
            }
        } else if (filter.getStoryId() != null && filter.getStoryId() > 0) {
            // 如果指定了storyId，则只查询该剧情的评论
            storyIds = List.of(filter.getStoryId());
        }

        // 将parentId为0的情况转换为null，表示查询顶级评论
        Long parentId = filter.getParentId();
        if (parentId != null && parentId == 0) {
            parentId = null;
        }

        PageInfo<AgentStoryCommentEntity> page = agentStoryCommentService.pageStoryCommentsAdmin(
                params.getCurrent(), params.getPageSize(), storyIds, parentId, filter.getStatus(), filter.getUsername());

        if (page.getList() != null && !page.getList().isEmpty()) {
            Set<String> usernames = page.getList().stream()
                    .flatMap(history -> Stream.of(history.getUsername(), history.getReplyToUsername()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Map<String, UserBaseContentVo> usrMap = findAllUserMap(new ArrayList<>(usernames));
            List<Long> refStoryIds = page.getList().stream()
                    .map(AgentStoryCommentEntity::getStoryId)
                    .collect(Collectors.toList());
            List<AgentStoryEntity> storyEntities = agentStoryService.findAllByIds(new ArrayList<>(refStoryIds));
            Map<Long, AgentStoryEntity> storyMap = storyEntities.stream()
                    .collect(Collectors.toMap(AgentStoryEntity::getId, entity -> entity));
            List<AgentStoryCommentVO> vos = page.getList().stream()
                    .map(entity -> convertToVO(entity, usrMap,storyMap))
                    .toList();
            PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
            return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
        }

        PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.success(new PageBaseContentVo<>(new ArrayList<>(), pagination));
    }
    /**
     * 获取评论回复列表
     */
    @Operation(summary = "通过父ID获取评论回复列表", description = "获取指定评论的回复列表")
    @PostMapping("/replies")
    public ResponseResult<List<AgentStoryCommentVO>> getCommentReplies(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        List<AgentStoryCommentEntity> replies = agentStoryCommentService.getCommentReplies(req.getId(), username);
        if(replies != null && !replies.isEmpty()){
            Set<String> usernames = replies.stream()
                    .flatMap(history -> Stream.of(history.getUsername(), history.getReplyToUsername()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Map<String, UserBaseContentVo> usrMap =findAllUserMap(new ArrayList<>(usernames));
            List<AgentStoryCommentVO> vos = replies.stream()
                    .map(entity -> convertToVO(entity,usrMap,new HashMap<>()))
                    .toList();

            return ResponseResult.success(vos);
        }
        return ResponseResult.success(new ArrayList<>());
    }


    /**
     * 获取剧情评论数
     */
    @Operation(summary = "获取剧情评论数", description = "获取指定剧情的评论数量")
    @PostMapping("/count")
    public ResponseResult<Integer> getCommentCount(@RequestBody @Valid ItemIdDTO req) {
        int count = agentStoryCommentService.getCommentCount(req.getId());
        return ResponseResult.success(count);
    }

    /**
     * 点赞/取消点赞评论
     */
    @Operation(summary = "点赞/取消点赞评论", description = "对评论进行点赞或取消点赞操作")
    @PostMapping("/like")
    public ResponseResult<Boolean> likeComment(@RequestBody @Valid AgentStoryCommentLikeDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        if (req.getLiked() == null) {
            // 切换点赞状态
            boolean result = commentLikeService.toggleLike(req.getCommentId(), username);
            return ResponseResult.success(result);
        } else if (req.getLiked()) {
            // 点赞
            commentLikeService.addLike(req.getCommentId(), username);
            return ResponseResult.success(true);
        } else {
            // 取消点赞
            commentLikeService.removeLike(req.getCommentId(), username);
            return ResponseResult.success(false);
        }
    }

    /**
     * 获取评论点赞数
     */
    @Operation(summary = "获取评论点赞数", description = "获取指定评论的点赞数量")
    @PostMapping("/like/count")
    public ResponseResult<Integer> getCommentLikeCount(@RequestBody @Valid ItemIdDTO req) {
        int count = commentLikeService.getLikeCount(req.getId());
        return ResponseResult.success(count);
    }

    /**
     * 检查用户是否已点赞评论
     */
    @Operation(summary = "检查用户是否已点赞评论", description = "检查当前用户是否已对指定评论点赞")
    @PostMapping("/like/status")
    public ResponseResult<Boolean> checkCommentLikeStatus(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        int status = commentLikeService.isLiked(req.getId(), username);
        return ResponseResult.success(status == 1);
    }

    @Operation(summary = "上传图片", responses = {
            @ApiResponse(responseCode = "200", description = "成功上传图片")
    })
    @PostMapping(value = "file/upload", consumes = "multipart/form-data")
    public ResponseResult<UploadFileVo> fileUpload(@RequestParam("file") MultipartFile file) {
        // 校验文件
        if (file == null || file.isEmpty()) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "文件不能为空");
        }

        // 校验文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !AgentFileConstants.ALLOWED_EXTENSIONS.contains(
                originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase())) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "不支持的文件类型");
        }

        // 校验文件大小
        if (file.getSize() > AgentFileConstants.UPLOAD_MAX_SIZE) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "文件大小超过限制");
        }
        
        // 处理文件上传
        String filePath = UploadFileUtil.handleFileUpload(file,
                AgentFileConstants.ALLOWED_EXTENSIONS,
                AgentFileConstants.UPLOAD_MAX_SIZE,
                null,
                AgentFileConstants.UPLOAD_STORY_COMMENT_PATH);
        try {
            log.info("图片审核: {}", filePath);
            securityCheckService.checkImageWithTencentCOS("剧情评论图片", filePath);
        } catch (Exception e) {
            log.error("图片违规: {}", e.getMessage(), e);
            // 删除违规图片
            UploadFileUtil.deleteFile(filePath);
            return ResponseResult.failure(VIOLATION_CODE.getCode(), "图片违规");
        }
        return ResponseResult.success(new UploadFileVo(filePath));
    }
}