package com.gw.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 敏感词导入结果VO
 */
@Data
@Schema(description = "敏感词导入结果VO")
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveWordImportResultVO {
    
    @Schema(description = "总行数")
    private Integer totalLines;
    
    @Schema(description = "成功导入数量")
    private Integer successCount;
    
    @Schema(description = "跳过数量（已存在）")
    private Integer skipCount;
    
    @Schema(description = "失败数量")
    private Integer failCount;
    
    @Schema(description = "跳过的敏感词列表")
    private List<String> skippedWords;
    
    @Schema(description = "失败的敏感词列表")
    private List<String> failedWords;
    
    @Schema(description = "导入详细信息")
    private String message;
}
