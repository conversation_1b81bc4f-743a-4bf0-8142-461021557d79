# 敏感词导入导出功能

## 功能概述

为SensitiveWordController添加了敏感词文件导入导出功能，支持txt格式文件，一行一个敏感词。

## 新增文件

### DTO类
1. **SensitiveWordImportDTO.java** - 敏感词导入参数
   - file: 敏感词文件
   - categoryId: 分类ID
   - level: 敏感词级别（1-低级，2-中级，3-高级，4-严重）
   - action: 处理动作（1-拦截，2-替换，3-警告）
   - replacement: 替换内容
   - status: 状态（0-禁用，1-启用）
   - overwrite: 是否覆盖已存在的敏感词

2. **SensitiveWordExportDTO.java** - 敏感词导出参数
   - categoryIds: 分类ID列表
   - levels: 敏感词级别列表
   - actions: 处理动作列表
   - status: 状态
   - format: 导出格式（1-仅敏感词，2-包含详细信息）
   - fileNamePrefix: 文件名前缀

### VO类
3. **SensitiveWordImportResultVO.java** - 敏感词导入结果
   - totalLines: 总行数
   - successCount: 成功导入数量
   - skipCount: 跳过数量
   - failCount: 失败数量
   - skippedWords: 跳过的敏感词列表
   - failedWords: 失败的敏感词列表
   - message: 导入详细信息

## 新增接口

### 1. 导入敏感词文件
```http
POST /api/v1/sensitive-word/import
Content-Type: multipart/form-data

参数：
- file: 敏感词文件（txt格式）
- categoryId: 分类ID（必填）
- level: 敏感词级别（默认1）
- action: 处理动作（默认1）
- replacement: 替换内容（可选）
- status: 状态（默认1）
- overwrite: 是否覆盖已存在的敏感词（默认false）

返回：SensitiveWordImportResultVO
```

### 2. 导出敏感词文件
```http
POST /api/v1/sensitive-word/export
Content-Type: application/json

请求体：SensitiveWordExportDTO

返回：文件下载（txt格式）
```

## 新增服务方法

### SensitiveWordService接口
1. `importFromFile()` - 从文件导入敏感词
2. `exportToFile()` - 导出敏感词到文件
3. `findForExport()` - 根据导出条件查询敏感词

### SensitiveWordServiceImpl实现
- 实现了文件读取、敏感词解析、批量导入
- 支持覆盖模式和跳过模式
- 实现了按条件导出功能
- 支持两种导出格式：仅敏感词和详细信息

## 使用示例

### 导入敏感词
```bash
curl -X POST "http://localhost:8080/api/v1/sensitive-word/import" \
  -F "file=@sw_民生.txt" \
  -F "categoryId=1" \
  -F "level=2" \
  -F "action=1" \
  -F "status=1" \
  -F "overwrite=false"
```

### 导出敏感词
```bash
curl -X POST "http://localhost:8080/api/v1/sensitive-word/export" \
  -H "Content-Type: application/json" \
  -d '{
    "categoryIds": [1],
    "format": 1,
    "fileNamePrefix": "sensitive_words"
  }' \
  --output sensitive_words.txt
```

## 文件格式

### 导入文件格式（txt）
```
敏感词1
敏感词2
敏感词3
...
```

### 导出文件格式
#### 格式1（仅敏感词）
```
敏感词1
敏感词2
敏感词3
...
```

#### 格式2（包含详细信息）
```
# 敏感词导出文件
# 导出时间: 2024-01-01 12:00:00
# 总数量: 100
# 格式: 敏感词|分类ID|级别|动作|替换内容|状态

敏感词1|1|2|1||1
敏感词2|1|3|2|***|1
...
```

## 注意事项

1. 导入文件必须是UTF-8编码的txt文件
2. 每行一个敏感词，空行会被忽略
3. 导入时会自动去重，已存在的敏感词根据overwrite参数决定是否覆盖
4. 导出文件名会自动添加时间戳
5. 导入导出操作会自动刷新敏感词检测服务的缓存
