package com.gw.chat.service;

import com.gw.chat.entity.ChatMessage;
import com.gw.chat.entity.ConversationSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 聊天消息数据迁移服务
 * 在应用启动时检测旧的chat_messages表，并将数据按agentId分配到新的动态表中
 *
 * 迁移流程：
 * 1. 启动检查：应用启动时自动执行（实现 ApplicationRunner）
 * 2. 迁移状态检查：通过 migration_lock 集合避免重复迁移
 * 3. 数据提取：从旧的 chat_messages 表中读取所有数据
 * 4. 按 agentId 分组：将消息按 agentId 进行分组
 * 5. 动态表迁移：每个 agentId 对应一个独立的集合 chat_messages_{agentId}
 * 6. 异常处理：处理没有 agentId 的消息（记录警告但跳过）
 * 7. 备份清理：迁移完成后备份原表并删除
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class ChatMessageMigrationService implements ApplicationRunner {

    private static final String OLD_COLLECTION_NAME = "chat_messages";
    private static final String MIGRATION_LOCK_COLLECTION = "migration_lock";
    private static final String MIGRATION_NAME = "chat_messages_migration";
    private static final int BATCH_SIZE = 1000; // 批处理大小
    private static final DateTimeFormatter BACKUP_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    private final MongoTemplate mongoTemplate;
    private final ChatMessageService chatMessageService;
    private final ChatService chatService;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("🚀 开始检查是否需要进行聊天消息数据迁移...");

        long startTime = System.currentTimeMillis();
        MigrationResult result = new MigrationResult();

        try {
            // 1. 启动检查：检查是否已经执行过迁移

            if (isMigrationCompleted()) {
                log.info("✅ 数据迁移已完成，跳过迁移过程");
                return;
            }

            // 2. 迁移状态检查：检查旧表是否存在
            if (!mongoTemplate.collectionExists(OLD_COLLECTION_NAME)) {
                log.info("ℹ️ 旧的{}表不存在，无需迁移", OLD_COLLECTION_NAME);
                markMigrationCompleted();
                return;
            }

            // 3. 数据提取和迁移
            result = performMigration();

            // 4. 标记迁移完成
            markMigrationCompleted();

            long duration = System.currentTimeMillis() - startTime;
            log.info("🎉 聊天消息数据迁移完成！总耗时: {}ms, 迁移统计: {}", duration, result);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ 聊天消息数据迁移失败，耗时: {}ms", duration, e);

            // 记录迁移失败信息
            recordMigrationFailure(e, result);
            throw e;
        }
    }

    /**
     * 检查迁移是否已完成
     */
    private boolean isMigrationCompleted() {
        return true;
//        try {
//            if (!mongoTemplate.collectionExists(MIGRATION_LOCK_COLLECTION)) {
//                return false;
//            }
//
//            Query query = new Query(Criteria.where("migrationName").is(MIGRATION_NAME));
//            long count = mongoTemplate.count(query, MIGRATION_LOCK_COLLECTION);
//
//            if (count > 0) {
//                MigrationLock lock = mongoTemplate.findOne(query, MigrationLock.class, MIGRATION_LOCK_COLLECTION);
//                log.info("📋 发现已完成的迁移记录: {}", lock);
//                return true;
//            }
//
//            return false;
//        } catch (Exception e) {
//            log.warn("⚠️ 检查迁移状态时发生异常，假设未完成迁移", e);
//            return false;
//        }
    }

    /**
     * 标记迁移已完成
     */
    private void markMigrationCompleted() {
        try {
            if (!mongoTemplate.collectionExists(MIGRATION_LOCK_COLLECTION)) {
                mongoTemplate.createCollection(MIGRATION_LOCK_COLLECTION);
                log.info("📁 创建迁移锁集合: {}", MIGRATION_LOCK_COLLECTION);
            }

            MigrationLock lock = new MigrationLock();
            lock.setMigrationName(MIGRATION_NAME);
            lock.setCompletedAt(LocalDateTime.now());
            lock.setStatus("COMPLETED");

            mongoTemplate.save(lock, MIGRATION_LOCK_COLLECTION);
            log.info("🔒 标记迁移完成: {}", lock);
        } catch (Exception e) {
            log.error("❌ 标记迁移完成失败", e);
            throw new RuntimeException("标记迁移完成失败", e);
        }
    }

    /**
     * 执行数据迁移
     */
    private MigrationResult performMigration() {
        log.info("🔄 开始执行聊天消息数据迁移...");

        MigrationResult result = new MigrationResult();
        result.migrationStartTime = LocalDateTime.now();
        long startTime = System.currentTimeMillis();

        try {
            // 1. 获取所有活跃会话
            List<ConversationSession> activeSessions = chatService.findAllActiveSessions();
            result.activeSessions = activeSessions.size();

            if (activeSessions.isEmpty()) {
                log.info("ℹ️ 没有找到活跃会话，无需迁移");
                result.migrationEndTime = LocalDateTime.now();
                return result;
            }

            log.info("📊 找到 {} 个活跃会话", activeSessions.size());

            // 2. 按会话逐个处理消息迁移（避免内存溢出）
            processSessionsOneByOne(activeSessions, result);

            // 6. 备份清理：迁移完成后备份原表并删除
//            backupOldCollection(result);

            result.duration = System.currentTimeMillis() - startTime;
            result.migrationEndTime = LocalDateTime.now();
            result.success = true;

            log.info("✅ 数据迁移完成！");
            log.info("📊 迁移统计: {}", result);
            log.info("🎯 智能体分布: 共 {} 个智能体，消息分布如下:", result.processedAgents);

            // 显示消息数量最多的前5个智能体
            result.agentMessageCounts.entrySet().stream()
                    .sorted((e1, e2) -> Integer.compare(e2.getValue(), e1.getValue()))
                    .limit(5)
                    .forEach(entry -> log.info("  agentId: {} -> {} 条消息", entry.getKey(), entry.getValue()));

            return result;

        } catch (Exception e) {
            result.duration = System.currentTimeMillis() - startTime;
            result.migrationEndTime = LocalDateTime.now();
            result.success = false;
            result.errorMessage = e.getMessage();
            log.error("❌ 数据迁移失败，耗时: {}ms", result.duration, e);
            throw e;
        }
    }

    /**
     * 按会话逐个处理消息迁移（避免内存溢出）
     */
    private void processSessionsOneByOne(List<ConversationSession> activeSessions, MigrationResult result) {
        try {
            log.info("🔄 开始按会话逐个处理消息迁移，共 {} 个会话", activeSessions.size());

            // 按智能体分组会话，便于统计
            Map<Long, List<ConversationSession>> sessionsByAgent = activeSessions.stream()
                    .filter(session -> session.getAgentId() != null)
                    .collect(Collectors.groupingBy(ConversationSession::getAgentId));

            result.processedAgents = sessionsByAgent.size();
            log.info("📊 涉及 {} 个智能体", result.processedAgents);

            // 统计信息
            AtomicInteger processedSessions = new AtomicInteger(0);
            AtomicInteger totalMigratedMessages = new AtomicInteger(0);
            AtomicInteger skippedSessions = new AtomicInteger(0);

            // 逐个处理每个会话
            for (ConversationSession session : activeSessions) {
                try {
                    if(session.getLastMessageTime() == null){
                        log.warn("会话 {} 的最后消息时间为空，跳过该会话", session.getId());
                        continue;
                    }
                    if(session.getMigratedAt() != null && session.getMigratedAt().isAfter(session.getLastMessageTime())){
                        log.warn("会话 {} 已迁移，跳过该会话", session.getId());
                        continue;
                    }
                    int sessionMessages = processSessionMessages(session, result);
                    session.setMigratedAt(session.getLastMessageTime());
                    mongoTemplate.save(session);
                    if (sessionMessages > 0) {
                        totalMigratedMessages.addAndGet(sessionMessages);

                        // 更新智能体消息计数
                        if (session.getAgentId() != null) {
                            result.agentMessageCounts.merge(session.getAgentId(), sessionMessages, Integer::sum);
                        }
                    } else {
                        skippedSessions.incrementAndGet();
                    }

                    processedSessions.incrementAndGet();

                    // 每处理100个会话输出一次进度
                    if (processedSessions.get() % 100 == 0) {
                        log.info("📈 进度: {}/{} 会话已处理，已迁移 {} 条消息",
                                processedSessions.get(), activeSessions.size(), totalMigratedMessages.get());
                    }

                } catch (Exception e) {
                    log.error("❌ 处理会话 {} (agentId: {}) 失败: {}",
                            session.getId(), session.getAgentId(), e.getMessage(), e);
                    skippedSessions.incrementAndGet();
                }
            }

            // 更新结果统计
            result.totalMessages = totalMigratedMessages.get();
            result.migratedMessages = totalMigratedMessages.get();
            result.skippedSessions = skippedSessions.get();

            log.info("✅ 会话处理完成: 总会话 {}, 已处理 {}, 跳过 {}, 迁移消息 {} 条",
                    activeSessions.size(), processedSessions.get(), skippedSessions.get(), totalMigratedMessages.get());

        } catch (Exception e) {
            log.error("❌ 按会话处理消息迁移失败", e);
            throw new RuntimeException("按会话处理消息迁移失败", e);
        }
    }

    /**
     * 处理单个会话的消息迁移
     */
    private int processSessionMessages(ConversationSession session, MigrationResult result) {
        try {
            String sessionId = session.getId();
            Long agentId = session.getAgentId();

            if (agentId == null) {
                log.debug("⚠️ 会话 {} 没有 agentId，跳过", sessionId);
                return 0;
            }

            // 查询该会话的所有消息
            Query query = new Query(Criteria.where("sessionId").is(sessionId));
            List<ChatMessage> sessionMessages = mongoTemplate.find(query, ChatMessage.class, OLD_COLLECTION_NAME);

            if (sessionMessages.isEmpty()) {
                log.debug("ℹ️ 会话 {} 没有历史消息", sessionId);
                return 0;
            }

            log.debug("📦 会话 {} (agentId: {}) 找到 {} 条消息", sessionId, agentId, sessionMessages.size());

            // 迁移该会话的所有消息（包含序列号逻辑）
            int migratedCount = 0;
            Long seqNum = 0L;
            String lastChatId = "";

            for (ChatMessage message : sessionMessages) {
                try {
                    // 确保消息有正确的 agentId
                    if (message.getAgentId() == null) {
                        message.setAgentId(agentId);
                    }

                    // 设置序列号逻辑
                    if ("user".equals(message.getRole())) {
                        lastChatId = message.getId() != null ? message.getId() : "";
                        message.setSeqNum(seqNum++);
                        session.setLstSeqNum(seqNum);
                    } else if (message.getLastChatId() != null && message.getLastChatId().equals(lastChatId)) {
                        message.setSeqNum(seqNum);
                    } else {
                        message.setSeqNum(seqNum++);
                    }

                    // 保存到对应的动态表
                    chatMessageService.save(message);
                    migratedCount++;

                } catch (Exception e) {
                    log.warn("⚠️ 迁移消息失败 - 会话: {}, 消息ID: {}, 错误: {}",
                            sessionId, message.getId(), e.getMessage());
                }
            }

            if (migratedCount > 0) {
                log.debug("✅ 会话 {} 成功迁移 {} 条消息", sessionId, migratedCount);
            }

            return migratedCount;

        } catch (Exception e) {
            log.error("❌ 处理会话 {} 的消息失败", session.getId(), e);
            throw e;
        }
    }

    /**
     * 基于活跃会话提取对应的历史消息（已废弃，改为按会话逐个处理）
     */
    @Deprecated
    private List<ChatMessage> extractMessagesFromActiveSessions(List<ConversationSession> activeSessions, MigrationResult result) {
        try {
            log.info("📥 开始基于 {} 个活跃会话从旧表 {} 提取历史消息...", activeSessions.size(), OLD_COLLECTION_NAME);

            // 1. 提取所有活跃会话的ID
            List<String> activeSessionIds = activeSessions.stream()
                    .map(ConversationSession::getId)
                    .collect(Collectors.toList());

            log.info("🔍 提取到 {} 个活跃会话ID", activeSessionIds.size());

            // 2. 按智能体分组会话，便于统计和日志
            Map<Long, List<ConversationSession>> sessionsByAgent = activeSessions.stream()
                    .filter(session -> session.getAgentId() != null)
                    .collect(Collectors.groupingBy(ConversationSession::getAgentId));

            log.info("📊 活跃会话分布: 共 {} 个智能体", sessionsByAgent.size());
            sessionsByAgent.forEach((agentId, sessions) ->
                    log.debug("  agentId: {} -> {} 个活跃会话", agentId, sessions.size()));

            // 3. 基于活跃会话ID查询历史消息
            List<ChatMessage> allMessages = new ArrayList<>();

            // 分批处理会话ID以避免查询条件过长
            int sessionBatchSize = 100; // 每批处理100个会话ID
            for (int i = 0; i < activeSessionIds.size(); i += sessionBatchSize) {
                int endIndex = Math.min(i + sessionBatchSize, activeSessionIds.size());
                List<String> sessionBatch = activeSessionIds.subList(i, endIndex);

                Query query = new Query(Criteria.where("sessionId").in(sessionBatch));
                List<ChatMessage> batchMessages = mongoTemplate.find(query, ChatMessage.class, OLD_COLLECTION_NAME);
                allMessages.addAll(batchMessages);

                log.debug("📦 处理会话批次 {}-{}, 找到 {} 条历史消息，累计 {} 条",
                        i + 1, endIndex, batchMessages.size(), allMessages.size());
            }

            // 4. 统计消息分布
            if (!allMessages.isEmpty()) {
                Map<Long, Long> messageCountsByAgent = allMessages.stream()
                        .filter(msg -> msg.getAgentId() != null)
                        .collect(Collectors.groupingBy(
                                ChatMessage::getAgentId,
                                Collectors.counting()
                        ));

                log.info("📊 历史消息分布: 共 {} 个智能体有历史消息", messageCountsByAgent.size());
                messageCountsByAgent.entrySet().stream()
                        .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                        .limit(10)
                        .forEach(entry ->
                                log.debug("  agentId: {} -> {} 条历史消息", entry.getKey(), entry.getValue()));
            }

            log.info("📊 基于活跃会话的历史消息提取完成，共 {} 条消息", allMessages.size());
            return allMessages;

        } catch (Exception e) {
            log.error("❌ 基于活跃会话提取历史消息失败", e);
            throw new RuntimeException("基于活跃会话提取历史消息失败", e);
        }
    }



    /**
     * 提取所有消息数据（备用方案）
     */
    private List<ChatMessage> extractAllMessages() {
        try {
            log.info("📥 备用方案：提取所有消息数据...");

            // 分批查询以避免内存溢出
            List<ChatMessage> allMessages = new ArrayList<>();
            Query query = new Query();
            query.limit(BATCH_SIZE);

            int skip = 0;
            List<ChatMessage> batch;

            do {
                query.skip(skip);
                batch = mongoTemplate.find(query, ChatMessage.class, OLD_COLLECTION_NAME);
                allMessages.addAll(batch);
                skip += BATCH_SIZE;

                if (!batch.isEmpty()) {
                    log.debug("📦 提取第 {} 批数据，本批 {} 条，累计 {} 条",
                            skip / BATCH_SIZE, batch.size(), allMessages.size());
                }
            } while (batch.size() == BATCH_SIZE);

            log.info("📊 全量数据提取完成，共 {} 条消息", allMessages.size());
            return allMessages;

        } catch (Exception e) {
            log.error("❌ 提取全量消息数据失败", e);
            throw new RuntimeException("提取全量消息数据失败", e);
        }
    }







    /**
     * 备份旧集合
     */
    private void backupOldCollection(MigrationResult result) {
        try {
            log.info("💾 开始备份旧集合...");

            String backupName = OLD_COLLECTION_NAME + "_backup_" +
                    LocalDateTime.now().format(BACKUP_DATE_FORMAT);

            // MongoDB没有直接的重命名操作，我们通过复制数据来备份
            List<ChatMessage> allMessages = mongoTemplate.findAll(ChatMessage.class, OLD_COLLECTION_NAME);

            if (!allMessages.isEmpty()) {
                // 分批插入备份数据
                for (int i = 0; i < allMessages.size(); i += BATCH_SIZE) {
                    int endIndex = Math.min(i + BATCH_SIZE, allMessages.size());
                    List<ChatMessage> batch = allMessages.subList(i, endIndex);
                    mongoTemplate.insert(batch, backupName);
                }

                result.backupCollection = backupName;
                log.info("✅ 已将 {} 条旧数据备份到集合: {}", allMessages.size(), backupName);
            }

            // 删除旧集合
            mongoTemplate.dropCollection(OLD_COLLECTION_NAME);
            log.info("🗑️ 已删除旧集合: {}", OLD_COLLECTION_NAME);

        } catch (Exception e) {
            log.warn("⚠️ 备份旧集合失败，但迁移已完成", e);
            result.backupFailed = true;
            result.backupError = e.getMessage();
        }
    }

    /**
     * 记录迁移失败信息
     */
    private void recordMigrationFailure(Exception e, MigrationResult result) {
        try {
            if (!mongoTemplate.collectionExists(MIGRATION_LOCK_COLLECTION)) {
                mongoTemplate.createCollection(MIGRATION_LOCK_COLLECTION);
            }

            MigrationLock lock = new MigrationLock();
            lock.setMigrationName(MIGRATION_NAME);
            lock.setCompletedAt(LocalDateTime.now());
            lock.setStatus("FAILED");
            lock.setErrorMessage(e.getMessage());
            lock.setMigrationResult(result);

            mongoTemplate.save(lock, MIGRATION_LOCK_COLLECTION);
            log.info("📝 已记录迁移失败信息");
        } catch (Exception recordException) {
            log.error("❌ 记录迁移失败信息时发生异常", recordException);
        }
    }

    /**
     * 迁移结果统计
     */
    @lombok.Data
    public static class MigrationResult {
        private int totalMessages = 0;
        private int migratedMessages = 0;
        private int skippedMessages = 0;
        private int skippedSessions = 0;
        private int targetTables = 0;
        private int activeSessions = 0;
        private int processedAgents = 0;
        private long duration = 0;
        private boolean success = false;
        private String errorMessage;
        private String backupCollection;
        private boolean backupFailed = false;
        private String backupError;
        private Map<Long, Integer> agentMessageCounts = new HashMap<>();
        private LocalDateTime migrationStartTime;
        private LocalDateTime migrationEndTime;

        @Override
        public String toString() {
            return String.format("总消息数: %d, 迁移成功: %d, 跳过消息: %d, 跳过会话: %d, 目标表数: %d, 活跃会话: %d, 处理智能体: %d, 耗时: %dms, 成功: %s%s",
                    totalMessages, migratedMessages, skippedMessages, skippedSessions, targetTables, activeSessions, processedAgents, duration, success,
                    backupCollection != null ? ", 备份: " + backupCollection : "");
        }
    }

    /**
     * 迁移锁实体
     */
    @lombok.Data
    public static class MigrationLock {
        @org.springframework.data.annotation.Id
        private String id;
        private String migrationName;
        private LocalDateTime completedAt;
        private String status; // COMPLETED, FAILED
        private String errorMessage;
        private MigrationResult migrationResult;

        @Override
        public String toString() {
            return String.format("迁移: %s, 状态: %s, 完成时间: %s",
                    migrationName, status, completedAt);
        }
    }
}