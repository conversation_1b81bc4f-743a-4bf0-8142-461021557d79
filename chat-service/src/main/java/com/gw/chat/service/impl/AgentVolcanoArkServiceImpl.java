package com.gw.chat.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.gw.chat.config.SpeechConfig;
import com.gw.chat.config.VolcanoArkConfig;
import com.gw.chat.constant.ChatConstant;
import com.gw.chat.dto.CreateRemoteSessionParamsDTO;
import com.gw.chat.dto.MessageContentDTO;
import com.gw.chat.exception.VolcanoContentParsingException;
import com.gw.chat.service.AgentCozeRemoteService;
import com.gw.chat.service.AgentRemoteVolvanoService;
import com.gw.chat.utils.ChatUtils;
import com.gw.chat.vo.AIResponseVO;
import com.gw.chat.vo.ChatContextVO;
import com.gw.chat.vo.TextToSpeechVO;
import com.gw.common.agent.vo.VolcanoArkSettingVO;
import com.gw.common.util.StringUtils;
import com.gw.common.util.UploadFileUtil;
import com.volcengine.ark.runtime.Const;
import com.volcengine.ark.runtime.exception.ArkHttpException;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;
import com.volcengine.ark.runtime.model.context.CreateContextResult;
import com.volcengine.ark.runtime.model.context.TruncationStrategy;
import com.volcengine.ark.runtime.model.context.chat.ContextChatCompletionRequest;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.FileTime;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

/**
 * 基于火山方舟大模型服务平台的AgentRemoteService实现
 * 支持上下文缓存功能，优化了性能和错误处理
 */
@Service("agentVolcanoArkService")
@Log4j2
@RequiredArgsConstructor
public class AgentVolcanoArkServiceImpl implements AgentRemoteVolvanoService {

    // 默认配置常量
    private static final int DEFAULT_COMPRESS_MAX_TOKENS = 16000;
    private static final int DEFAULT_MAX_TOKENS = 320;
    private static final double DEFAULT_TEMPERATURE = 0.7;
    private static final String DEFAULT_AUDIO_DURATION = "0:00";
    private final SpeechConfig speechConfig;
    private final VolcanoArkConfig volcanoArkConfig;
    private final Object arkServiceLock = new Object();
    private final AgentCozeRemoteService agentCozeRemoteService;
    // 标记上下文缓存功能是否可用
    // ArkService 实例缓存，避免重复创建
    private volatile ArkService arkServiceInstance;
    private  final AgentCozeRemoteService cozeRemoteService;

    private static String getResponseContent(ChatCompletionResult response) throws VolcanoContentParsingException {
        if (response == null || response.getChoices() == null || response.getChoices().isEmpty()) {
            throw VolcanoContentParsingException.emptyResponse();
        }
        var choice = response.getChoices().get(0);
        if(choice.getFinishReason().equals("length")){
            log.warn("response:{}", response);
            throw VolcanoContentParsingException.contentTooLong();
        }

        var responseMessage = choice.getMessage();

        if (responseMessage == null || responseMessage.getContent() == null) {
            throw VolcanoContentParsingException.emptyContent();
        }

        return String.valueOf(responseMessage.getContent());
    }

    /**
     * 异步计算目录大小
     */
    @Async("taskExecutor")
    public CompletableFuture<Long> calculateDirectorySizeAsync(Path directory) {
        return CompletableFuture.supplyAsync(() -> calculateDirectorySize(directory));
    }

    /**
     * 计算目录大小
     */
    private long calculateDirectorySize(Path directory) {
        try {
            return Files.walk(directory)
                    .filter(Files::isRegularFile)
                    .mapToLong(path -> {
                        try {
                            return Files.size(path);
                        } catch (IOException e) {
                            log.error("获取文件大小时出错: {}", path, e);
                            return 0L;
                        }
                    })
                    .sum();
        } catch (IOException e) {
            log.error("计算目录大小时出错: {}", directory, e);
            return 0L;
        }
    }

    /**
     * 异步清理旧文件
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> cleanupOldFilesAsync(Path directory) {
        return CompletableFuture.runAsync(() -> cleanupOldFiles(directory));
    }

    /**
     * 清理旧文件
     */
    private void cleanupOldFiles(Path directory) {
        try (Stream<Path> pathStream = Files.walk(directory)) {
            // 先收集文件信息，避免在排序时文件被删除
            List<FileInfo> fileInfos = pathStream
                    .filter(Files::isRegularFile)
                    .map(path -> {
                        try {
                            return new FileInfo(path, Files.getLastModifiedTime(path));
                        } catch (IOException e) {
                            // 文件可能在检查过程中被删除，跳过此文件
                            log.debug("文件在清理扫描过程中不再存在: {}", path);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)  // 过滤掉null值
                    .sorted(Comparator.comparing(FileInfo::lastModifiedTime))
                    .toList();

            // 删除最旧的文件直到目录大小低于限制
            long currentSize = calculateDirectorySize(directory);
            int deletedCount = 0;

            for (FileInfo fileInfo : fileInfos) {
                if (currentSize <= speechConfig.getMaxDirSize()) {
                    break;
                }

                Path file = fileInfo.path();
                try {
                    // 再次检查文件是否存在，因为可能在收集信息后被删除
                    if (!Files.exists(file)) {
                        log.debug("文件在清理过程中不再存在: {}", file);
                        continue;
                    }

                    long fileSize = Files.size(file);
                    Files.delete(file);
                    currentSize -= fileSize;
                    deletedCount++;
                    log.debug("删除旧文件: {}", file);
                } catch (IOException e) {
                    log.error("删除文件时出错: {}", file, e);
                }
            }

            if (deletedCount > 0) {
                log.info("清理完成，删除了 {} 个旧文件，当前目录大小: {} bytes", deletedCount, currentSize);
            }
        } catch (IOException e) {
            log.error("清理旧文件时出错: {}", directory, e);
        }
    }

    /**
     * 文件信息记录类，用于安全地处理文件排序
     */
    private record FileInfo(Path path, FileTime lastModifiedTime) {}

    /**
     * 获取火山方舟服务实例（使用单例模式避免重复创建）
     */
    private ArkService getArkService() {
        if (arkServiceInstance == null) {
            synchronized (arkServiceLock) {
                if (arkServiceInstance == null) {
                    VolcanoArkSettingVO setting = getVolcanoArkSetting();
                    arkServiceInstance = ArkService.builder()
                            .apiKey(setting.getApiKey())
                            .baseUrl(setting.getBaseUrl())
                            .timeout(Duration.ofMillis(setting.getReadTimeout()))
                            .connectTimeout(Duration.ofMillis(setting.getConnectTimeout()))
                            .retryTimes(setting.getRetryTimes())
                            .build();
                    log.info("创建火山方舟服务实例，配置: baseUrl={}, modelId={}, timeout={}ms",
                            setting.getBaseUrl(), setting.getModelId(), setting.getReadTimeout());
                }
            }
        }
        return arkServiceInstance;
    }

    /**
     * 获取火山方舟配置
     */
    private VolcanoArkSettingVO getVolcanoArkSetting() {
        VolcanoArkSettingVO setting = new VolcanoArkSettingVO();
        setting.setApiKey(volcanoArkConfig.getApiKey());
        setting.setBaseUrl(volcanoArkConfig.getBaseUrl());
        setting.setModelId(volcanoArkConfig.getDefaultModelId());
        setting.setConnectTimeout(volcanoArkConfig.getConnectTimeout());
        setting.setReadTimeout(volcanoArkConfig.getReadTimeout());
        setting.setRetryTimes(volcanoArkConfig.getRetryTimes());
        setting.setCompressModelId(volcanoArkConfig.getCompressModelId());
        return setting;
    }

    /**
     * 创建上下文缓存
     * 如果上下文缓存功能不可用，会抛出异常
     */
    private CreateContextResult createContextCache(String modelId, List<ChatMessage> systemMessages) throws Exception {


        ArkService arkService = getArkService();
        TruncationStrategy truncationStrategy = TruncationStrategy.builder()
                .type(Const.TRUNCATION_STRATEGY_TYPE_ROLLING_TOKENS)
                .maxWindowTokens(32768)
                .rollingWindowTokens(4096)
                .rollingTokens(true)
                .build();

        // 构建请求 - 使用SDK提供的CreateContextRequest
        CreateContextRequest request = CreateContextRequest.builder()
                .model(modelId)
                // 设置上下文模式为会话模式
                .mode(Const.CONTEXT_MODE_SESSION)
                // 设置系统消息
                .messages(systemMessages)
                // 设置上下文的生存时间（秒）
                .truncationStrategy(truncationStrategy)
                .ttl((int) volcanoArkConfig.getContextCacheTtl())
                .build();

        log.debug("创建上下文缓存请求: modelId={}, systemMessagesCount={}, ttl={}",
                modelId, systemMessages.size(), volcanoArkConfig.getContextCacheTtl());

        try {
            // 使用SDK接口创建上下文缓存
            CreateContextResult result = arkService.createContext(request);
            log.info("创建上下文缓存成功: contextId={}", result.getId());
            return result;
        } catch (ArkHttpException e) {
            log.error("创建上下文缓存失败: code={}, message={}", e.code, e.getMessage());
            if ("ResourceNotFound".equals(e.code) || e.getMessage().contains("404")) {

                log.warn("检测到上下文缓存功能不可用，已标记为禁用状态");
            }
            throw new Exception("创建上下文缓存失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String createConversation(List<MessageContentDTO> messages) {
        String conversationId = UUID.randomUUID().toString();
        log.info("创建火山方舟会话: conversationId={}, systemMessagesCount={}",
                conversationId, messages != null ? messages.size() : 0);



        try {
            VolcanoArkSettingVO setting = getVolcanoArkSetting();

            // 创建系统消息 - 使用SDK的ChatMessage
            List<ChatMessage> systemMessages = new ArrayList<>();
            if (messages != null) {
                messages.forEach(msg -> {
                    if (msg != null && StringUtils.hasText(msg.getContent())) {
                        systemMessages.add(ChatMessage.builder()
                                .role(ChatMessageRole.SYSTEM)
                                .content(msg.getContent())
                                .build());
                    }
                });
            }

            // 尝试创建上下文缓存
            CreateContextResult contextResult = createContextCache(setting.getModelId(), systemMessages);


            log.info("创建火山方舟会话成功 - conversationId={}, contextCacheId={}",
                    conversationId, contextResult.getId());
            return contextResult.getId();

        } catch (Exception e) {
            log.warn("创建火山方舟上下文缓存失败，回退到传统模式: conversationId={}, error={}",
                    conversationId, e.getMessage());

            // 返回普通的UUID作为会话ID，使用传统模式
            return conversationId;
        }
    }

    /**
     * 使用上下文缓存进行对话
     */
    private String chatWithContextCache(String contextCacheId, List<ChatMessage> messages) throws Exception {
        ArkService arkService = getArkService();
        VolcanoArkSettingVO setting = getVolcanoArkSetting();

        // 构建请求 - 使用SDK提供的ContextChatCompletionRequest
        ContextChatCompletionRequest request = ContextChatCompletionRequest.builder()
                .model(setting.getModelId())
                .contextId(contextCacheId)
                .messages(messages)
                .maxTokens(DEFAULT_MAX_TOKENS)
                .temperature(DEFAULT_TEMPERATURE)
                .stream(false)
                .build();

        log.debug("上下文缓存对话请求: contextId={}, messagesCount={}, maxTokens={}",
                contextCacheId, messages.size(), DEFAULT_MAX_TOKENS);

        // 使用SDK接口进行上下文缓存对话
        try {
            var response = arkService.createContextChatCompletion(request);

            // 从响应中提取内容
            if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {

                log.info("上下文缓存对话响应: contextId={}, response={}",
                        contextCacheId, response);
                var choice = response.getChoices().get(0);
                if(choice.getFinishReason().equals("length")){
                    log.warn("response:{}", response);
                    throw VolcanoContentParsingException.contentTooLong();
                }
                var message = choice.getMessage();
                if (message != null && message.getContent() != null) {
                    String content = String.valueOf(message.getContent());
                    log.debug("上下文缓存对话成功: contextId={}, content={}",
                            contextCacheId, content);
                    return content;
                }
            }

            log.warn("上下文缓存对话响应为空: contextId={}", contextCacheId);
            throw new Exception("响应内容为空");

        } catch (ArkHttpException e) {
            log.error("上下文缓存对话异常: contextId={}, code={}, message={}",
                    contextCacheId, e.code, e.getMessage());

            if ("ResourceNotFound".equals(e.code)) {

                log.warn("上下文缓存资源未找到，标记功能为不可用: contextId={}", contextCacheId);
                return "ResourceNotFound";
            }
            throw VolcanoContentParsingException.emptyResponse();
        }
    }

    /**
     * 文本转语音功能
     */
    @Override
    public TextToSpeechVO textConvertSpeech(String text, String voiceId) {
        if (!StringUtils.hasText(text)) {
            log.warn("文本转语音失败：文本内容为空");
            return createEmptyTextToSpeechVO();
        }

        log.info("火山方舟文本转语音 - 文本长度: {}, 音色ID: {}", text.length(), voiceId);

        String content = StringUtils.removeParenthesisContent(text);
        log.debug("处理后的文本长度: {}", content.length());

        Path uploadPath = UploadFileUtil.buildUploadPath(null, speechConfig.getTempPath());

        // 异步检查目录大小并在需要时清理
        CompletableFuture<Long> sizeCheckFuture = calculateDirectorySizeAsync(uploadPath);
        sizeCheckFuture.thenAccept(currentSize -> {
            if (currentSize >= speechConfig.getMaxDirSize()) {
                log.info("目录大小 {} 超过限制 {}，开始异步清理旧文件", currentSize, speechConfig.getMaxDirSize());
                cleanupOldFilesAsync(uploadPath);
            }
        }).exceptionally(throwable -> {
            log.error("检查目录大小时出错", throwable);
            return null;
        });

        try {
            // TODO: 实际的语音合成逻辑需要集成火山方舟的语音服务
            // 这里可以集成火山引擎的语音合成API
            TextToSpeechVO vo = createEmptyTextToSpeechVO();

            log.warn("火山方舟语音合成功能尚未完全实现，返回空结果");
            return vo;
        } catch (Exception e) {
            log.error("处理语音文件时出错: text={}, voiceId={}", text, voiceId, e);
            return createEmptyTextToSpeechVO();
        }
    }

    /**
     * 创建空的文本转语音结果
     */
    private TextToSpeechVO createEmptyTextToSpeechVO() {
        TextToSpeechVO vo = new TextToSpeechVO();
        vo.setUrl(""); // 暂时返回空URL
        vo.setDuration(DEFAULT_AUDIO_DURATION); // 暂时返回0时长
        return vo;
    }

    /**
     * 构建聊天消息列表
     */
    private List<ChatMessage> buildChatMessages(List<ChatContextVO> contextMessages) {
        List<ChatMessage> messages = new ArrayList<>();

        if (contextMessages == null) {
            return messages;
        }

        for (ChatContextVO item : contextMessages) {
            if (item.getRole() == null || !StringUtils.hasText(item.getContent())) {
                continue;
            }

            ChatMessageRole role = mapToArkRole(item.getRole());
            if (role != null) {
                messages.add(ChatMessage.builder()
                        .role(role)
                        .content(item.getContent())
                        .build());
            }
        }

        return messages;
    }

    /**
     * 映射角色到火山方舟SDK的角色类型
     */
    private ChatMessageRole mapToArkRole(String role) {
        return switch (role) {
            case ChatConstant.CHAT_USER_ROLE -> ChatMessageRole.USER;
            case ChatConstant.CHAT_ASSISTANT_ROLE -> ChatMessageRole.ASSISTANT;
            case ChatConstant.CHAT_SYSTEM_ROLE -> ChatMessageRole.SYSTEM;
            default -> {
                log.warn("未知的消息角色: {}", role);
                yield null;
            }
        };
    }

    /**
     * 构建AI响应对象
     */
    private AIResponseVO buildAIResponse(String contextCacheId, String responseContent, boolean audio, String voiceId) {
        AIResponseVO aiResponse = new AIResponseVO();
        aiResponse.setRemoteContextId(contextCacheId);
        aiResponse.setStatus("completed");
        aiResponse.setCreatedAt(LocalDateTime.now());
        aiResponse.setCompletedAt(LocalDateTime.now());

        List<ChatContextVO> contexts = new ArrayList<>();

        // 处理语音转换
        String audioUrl = null;
        String audioDuration = "";
        if (audio && StringUtils.hasText(voiceId)) {
            try {

                String content = ChatUtils.removeBracketContent(responseContent);
                TextToSpeechVO speechResult = agentCozeRemoteService.textConvertSpeech(content, voiceId);

//                TextToSpeechVO audioInfo = textConvertSpeech(content, voiceId);
                if (speechResult != null) {
                    audioUrl = speechResult.getUrl();
                    audioDuration = speechResult.getDuration();
                }
            } catch (Exception e) {
                log.error("语音转换失败: voiceId={}, error={}", voiceId, e.getMessage());
            }
        }

        ChatContextVO context = ChatContextVO.builder()
                .role(ChatConstant.CHAT_ASSISTANT_ROLE)
                .type(ChatConstant.CHAT_MSG_ANSWER_TYPE)
                .content(responseContent)
                .contentType(ChatConstant.CHAT_CONTENT_TYPE_TEXT)
                .audioUrl(audioUrl)
                .audioDuration(audioDuration)
                .build();

        contexts.add(context);
        aiResponse.setContexts(contexts);
        aiResponse.setFollowMsg(new JSONArray());

        return aiResponse;
    }




    @Override
    public AIResponseVO sendChatMessage(String conversationId, List<ChatContextVO> message,
                                        boolean audio, String voiceId, ResourceNotFoundCallback callback) throws Exception {

        if (!StringUtils.hasText(conversationId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }

        log.debug("火山方舟发送聊天消息（带回调） - conversationId={}, messagesCount={}, audio={}, voiceId={}",
                conversationId, message != null ? message.size() : 0, audio, voiceId);

        return sendChatMessageWithContextCacheAndCallback(conversationId, message, audio, voiceId, callback);
    }

    /**
     * 使用上下文缓存进行对话（带回调）
     */
    private AIResponseVO sendChatMessageWithContextCacheAndCallback(String conversationId,
                                                                   List<ChatContextVO> message, boolean audio, String voiceId,
                                                                   ResourceNotFoundCallback callback) throws Exception {

        if (message == null || message.isEmpty()) {
            throw new IllegalArgumentException("消息列表不能为空");
        }

        log.debug("开始上下文缓存对话（带回调）: conversationId={}, messagesCount={}, audio={}",
                conversationId, message.size(), audio);

        // 获取或创建上下文缓存ID
        String contextCacheId = conversationId;

        // 构建聊天消息列表
        List<ChatMessage> messages = buildChatMessages(message);

        if (messages.isEmpty()) {
            throw new IllegalArgumentException("没有有效的消息内容");
        }

        try {
            // 调用上下文缓存对话API
            String responseContent = chatWithContextCache(contextCacheId, messages);

            // 如果上下文缓存资源未找到，触发回调并尝试重新创建
            if ("ResourceNotFound".equals(responseContent)) {
                log.info("上下文缓存资源未找到，触发回调: contextCacheId={}", contextCacheId);

                // 构建系统上下文信息
                SystemContext systemContext = new SystemContext(
                    contextCacheId,
                    message
                );
                CreateRemoteSessionParamsDTO sessionParams = null;
                // 触发回调
                if (callback != null) {
                    try {
                        sessionParams = callback.onResourceNotFound(conversationId, systemContext, "上下文缓存资源未找到");
                        if (sessionParams != null && sessionParams.getContexts() != null) {
                            for(MessageContentDTO item: sessionParams.getContexts()){
                                log.info("回调返回的系统消息: {}", item.getContent());
                            }
                        }
                    } catch (Exception callbackException) {
                        log.error("回调执行异常: conversationId={}", conversationId, callbackException);
                    }
                }
                if(sessionParams == null || sessionParams.getContexts() == null){
                    throw new Exception("上下文缓存功能不可用");
                }
                // 尝试重新创建上下文缓存
                String newContextCacheId = createConversation(sessionParams.getContexts());
                responseContent = chatWithContextCache(newContextCacheId, messages);

                if ("ResourceNotFound".equals(responseContent)) {
                    log.error("重新创建上下文缓存后仍然失败，回退到传统模式");
                    throw new Exception("上下文缓存功能不可用");
                }
                contextCacheId = newContextCacheId;
            }

            // 构建AI响应
            AIResponseVO aiResponse = buildAIResponse(contextCacheId, responseContent, audio, voiceId);

            log.info("上下文缓存对话完成（带回调）: contextCacheId={}, responseLength={}",
                    contextCacheId, responseContent.length());
            return aiResponse;

        }catch (VolcanoContentParsingException e) {
            throw e;
        } catch (Exception e) {
            log.error("上下文缓存对话失败，回退到传统模式: conversationId={}", conversationId, e);
            // 如果上下文缓存对话失败，回退到传统模式
            return sendChatMessageTraditional(message, audio, voiceId);
        }
    }

    /**
     * 传统方式进行对话（不使用上下文缓存）
     */
    private AIResponseVO sendChatMessageTraditional(List<ChatContextVO> message, boolean audio, String voiceId) throws Exception {

        log.info("使用传统模式进行对话: messagesCount={}, audio={}",
                message != null ? message.size() : 0, audio);

        if (message == null || message.isEmpty()) {
            throw new IllegalArgumentException("消息列表不能为空");
        }

        ArkService arkService = getArkService();
        VolcanoArkSettingVO setting = getVolcanoArkSetting();

        // 构建聊天消息列表
        List<ChatMessage> chatMessages = buildChatMessages(message);

        if (chatMessages.isEmpty()) {
            throw new IllegalArgumentException("没有有效的消息内容");
        }

        // 构建请求
        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(setting.getModelId())
                .messages(chatMessages)
                .maxTokens(DEFAULT_MAX_TOKENS)
                .temperature(DEFAULT_TEMPERATURE)
                .build();

        log.debug("发送传统模式请求到火山方舟: modelId={}, messagesCount={}",
                setting.getModelId(), chatMessages.size());

        try {
            // 调用火山方舟API
            ChatCompletionResult response = arkService.createChatCompletion(request);

            String responseContent = getResponseContent(response);

            // 构建AI响应
            AIResponseVO aiResponse = buildAIResponse(null, responseContent, audio, voiceId);

            log.info("传统模式对话完成: responseLength={}, contextsCount={}",
                    responseContent.length(), aiResponse.getContexts().size());
            return aiResponse;

        } catch (VolcanoContentParsingException e) {
            log.error("火山方舟内容解析异常: modelId={}, errorCode={}, error={}",
                    setting.getModelId(), e.getErrorCode(), e.getMessage());
            // 直接抛出专用异常，保留原始错误信息
            throw e;
        } catch (Exception e) {
            log.error("调用火山方舟API时出错: modelId={}, error={}", setting.getModelId(), e.getMessage());

            // 构建错误响应
            AIResponseVO errorResponse = new AIResponseVO();
            errorResponse.setStatus("failed");
            errorResponse.setCreatedAt(LocalDateTime.now());
            errorResponse.setFailedAt(LocalDateTime.now());
            errorResponse.setContexts(new ArrayList<>());
            errorResponse.setFollowMsg(new JSONArray());

            throw new Exception("火山方舟API调用失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String sendCompressChatMessage(List<ChatContextVO> message) throws Exception {

        log.info("压缩提取会话: messagesCount={}", message != null ? message.size() : 0);

        if (message == null || message.isEmpty()) {
            throw new IllegalArgumentException("消息列表不能为空");
        }

        ArkService arkService = getArkService();
        VolcanoArkSettingVO setting = getVolcanoArkSetting();

        // 构建聊天消息列表
        List<ChatMessage> chatMessages = buildChatMessages(message);

        if (chatMessages.isEmpty()) {
            throw new IllegalArgumentException("没有有效的消息内容");
        }

        // 构建请求
        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(setting.getCompressModelId())
                .messages(chatMessages)
                .maxTokens(DEFAULT_COMPRESS_MAX_TOKENS)
                .temperature(DEFAULT_TEMPERATURE)
                .build();

        log.debug("发送压缩模式请求到火山方舟: modelId={}, messagesCount={}",
                setting.getModelId(), chatMessages.size());

        try {
            // 调用火山方舟API
            var response = arkService.createChatCompletion(request);

            String responseContent = getResponseContent(response);

            // 构建AI响应

            log.info("压缩模式对话完成: responseLength={}, responseContent={}",
                    responseContent.length(), responseContent);
            return responseContent;

        } catch (VolcanoContentParsingException e) {
            log.error("火山方舟内容解析异常: modelId={}, errorCode={}, error={}",
                    setting.getModelId(), e.getErrorCode(), e.getMessage());
            // 直接抛出专用异常，保留原始错误信息
            throw e;
        } catch (Exception e) {
            log.error("调用火山方舟API进行压缩时出错: modelId={}, error={}", setting.getModelId(), e.getMessage());
            throw new Exception("火山方舟API调用失败: " + e.getMessage(), e);
        }
    }

}