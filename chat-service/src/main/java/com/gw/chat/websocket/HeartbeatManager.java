package com.gw.chat.websocket;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gw.chat.dto.ChatWsMsgRspDTO;
import com.gw.common.ws.WebSocketErrorCode;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * WebSocket心跳管理器
 * 负责维护WebSocket连接的心跳机制，确保连接的活跃性和可用性
 */
@Component
@Log4j2
public class HeartbeatManager {

    private final ObjectMapper objectMapper;
    private final ChatWebSocketSessionManager sessionManager;
    // 心跳统计
    private final AtomicLong totalHeartbeatsSent = new AtomicLong(0);
    private final AtomicLong totalHeartbeatsSuccess = new AtomicLong(0);
    private final AtomicLong totalHeartbeatsFailed = new AtomicLong(0);
    // 用户最后心跳响应时间
    private final Map<String, Long> lastHeartbeatResponse = new ConcurrentHashMap<>();
    private ScheduledExecutorService heartbeatExecutor;
    // 心跳配置
    @Value("${websocket.heartbeat.enabled:true}")
    private boolean heartbeatEnabled;
    @Value("${websocket.heartbeat.interval-seconds:30}")
    private int heartbeatIntervalSeconds;
    @Value("${websocket.heartbeat.timeout-seconds:90}")
    private int heartbeatTimeoutSeconds;

    @Autowired
    public HeartbeatManager(ObjectMapper objectMapper,
                            ChatWebSocketSessionManager sessionManager) {
        this.objectMapper = objectMapper;
        this.sessionManager = sessionManager;
    }

    @PostConstruct
    public void init() {
        if (heartbeatEnabled) {
            // 创建内部心跳执行器
            this.heartbeatExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "websocket-heartbeat-executor");
                t.setDaemon(true);
                return t;
            });

            startHeartbeatTask();
            log.info("✅ WebSocket心跳管理器已启动，心跳间隔: {}秒，超时时间: {}秒",
                    heartbeatIntervalSeconds, heartbeatTimeoutSeconds);
        } else {
            log.info("❌ WebSocket心跳功能已禁用");
        }
    }

    @PreDestroy
    public void destroy() {
        if (heartbeatExecutor != null && !heartbeatExecutor.isShutdown()) {
            heartbeatExecutor.shutdown();
            try {
                if (!heartbeatExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    heartbeatExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                heartbeatExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("💔 WebSocket心跳管理器已关闭");
    }

    /**
     * 启动心跳任务
     */
    private void startHeartbeatTask() {
        heartbeatExecutor.scheduleWithFixedDelay(
                this::sendHeartbeatToAllSessions,
                heartbeatIntervalSeconds, // 初始延迟
                heartbeatIntervalSeconds, // 执行间隔
                TimeUnit.SECONDS
        );
        log.debug("📅 心跳任务已启动");
    }

    /**
     * 向所有活跃会话发送心跳
     */
    private void sendHeartbeatToAllSessions() {
        try {
            Map<String, SessionMetadata> activeSessions = sessionManager.getAllActiveSessions();

            if (activeSessions.isEmpty()) {
//                log.info("💗 没有活跃会话，跳过心跳发送");
                return;
            }

            long currentTime = System.currentTimeMillis();
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);

            log.info("💗 开始发送心跳，目标会话数: {}, 心跳间隔: {}秒", activeSessions.size(), heartbeatIntervalSeconds);

            for (Map.Entry<String, SessionMetadata> entry : activeSessions.entrySet()) {
                String username = entry.getKey();
                SessionMetadata metadata = entry.getValue();

                try {
                    if (metadata == null || !metadata.isValid()) {
                        continue;
                    }

                    // 检查会话是否超时
                    if (isSessionTimeout(username, currentTime)) {
                        // 增加心跳失败计数
                        metadata.incrementHeartbeatFailure();

                        // 如果失败次数超过阈值，才移除会话
                        if (metadata.getHeartbeatFailureCount() >= 3) {
                            log.warn("⚠️ 会话多次心跳失败，移除用户: {}, 失败次数: {}",
                                    username, metadata.getHeartbeatFailureCount());
                            sessionManager.removeSession(username);
                        } else {
                            log.debug("⚠️ 会话心跳失败，但未达到移除阈值: {}, 失败次数: {}",
                                     username, metadata.getHeartbeatFailureCount());
                        }
                        continue;
                    }

                    // 发送心跳消息
                    boolean sent = sendHeartbeatToUser(username, currentTime);
                    if (sent) {
                        successCount.incrementAndGet();
                        totalHeartbeatsSuccess.incrementAndGet();
                        // 重置心跳失败计数
                        metadata.setHeartbeatFailureCount(0);
                    } else {
                        failedCount.incrementAndGet();
                        totalHeartbeatsFailed.incrementAndGet();
                        // 增加心跳失败计数
                        metadata.incrementHeartbeatFailure();
                    }

                    totalHeartbeatsSent.incrementAndGet();

                } catch (Exception e) {
                    log.warn("⚠️ 向用户 {} 发送心跳失败: {}", username, e.getMessage());
                    failedCount.incrementAndGet();
                    totalHeartbeatsFailed.incrementAndGet();

                    // 增加心跳失败计数
                    if (metadata != null) {
                        metadata.incrementHeartbeatFailure();
                    }
                }
            }

            if (activeSessions.size() > 0) {
                log.info("💗 心跳发送完成 - 目标: {}, 成功: {}, 失败: {}, 总发送: {}, 总成功: {}, 总失败: {}",
                        activeSessions.size(), successCount.get(), failedCount.get(),
                        totalHeartbeatsSent.get(), totalHeartbeatsSuccess.get(), totalHeartbeatsFailed.get());
            }

        } catch (Exception e) {
            log.error("❌ 发送心跳消息时发生严重异常", e);
        }
    }

    /**
     * 向指定用户发送心跳消息
     */
    private boolean sendHeartbeatToUser(String username, long currentTime) {
        try {
            // 构建心跳消息
            ChatWsMsgRspDTO heartbeatMsg = ChatWsMsgRspDTO.builder()
                    .cmd("heartbeat")
                    .code(WebSocketErrorCode.SUCCESS_CODE)
                    .error("心跳")
                    .msg(createHeartbeatData(currentTime, username))
                    .build();

            String messageJson = objectMapper.writeValueAsString(heartbeatMsg);

            log.debug("💗 发送心跳消息给用户: {}, 消息: {}", username, messageJson);

            // 通过会话管理器发送心跳
            boolean sent = sessionManager.sendMessageToUserWithResult(username, messageJson);

            if (sent) {
                // 更新会话活跃时间
                sessionManager.updateUserActiveTime(username);
                log.debug("💗 心跳消息发送成功: user={}", username);
            } else {
                log.warn("⚠️ 心跳消息发送失败: user={}", username);
            }

            return sent;

        } catch (Exception e) {
            log.warn("⚠️ 构建或发送心跳消息失败: user={}, error={}", username, e.getMessage());
            return false;
        }
    }

    /**
     * 创建心跳数据
     */
    private JSONObject createHeartbeatData(long currentTime, String username) {
        JSONObject heartbeatData = new JSONObject();
        heartbeatData.put("timestamp", currentTime);
        heartbeatData.put("username", username);
        heartbeatData.put("type", "heartbeat");
        heartbeatData.put("serverTime", System.currentTimeMillis());
        return heartbeatData;
    }

    /**
     * 检查会话是否超时 - 修复版本，兼容没有心跳响应的客户端
     */
    private boolean isSessionTimeout(String username, long currentTime) {
        Long lastResponse = lastHeartbeatResponse.get(username);
        SessionMetadata metadata = sessionManager.getSessionMetadata(username);

        if (metadata == null) {
            log.debug("用户 {} 没有会话元数据，认为超时", username);
            return true; // 没有会话元数据，认为超时
        }

        // 获取心跳失败次数
        int heartbeatFailures = metadata.getHeartbeatFailureCount();

        // 使用更宽松的超时策略
        long baseTimeoutMs = heartbeatTimeoutSeconds * 1000L;
        long timeoutMs;

        // 如果客户端从未响应过心跳，使用更宽松的策略
        if (lastResponse == null) {
            // 客户端可能没有实现心跳响应，基于会话活跃时间判断
            // 使用更长的超时时间（3倍心跳超时时间）
            timeoutMs = baseTimeoutMs * 3;
            log.debug("用户 {} 从未响应心跳，使用宽松超时策略: {}ms", username, timeoutMs);
        } else {
            // 客户端有心跳响应，使用渐进式超时策略
            if (heartbeatFailures == 0) {
                timeoutMs = baseTimeoutMs; // 正常超时时间
            } else if (heartbeatFailures <= 2) {
                timeoutMs = (baseTimeoutMs * 2) / 3; // 2/3超时时间
            } else {
                timeoutMs = baseTimeoutMs / 2; // 一半超时时间
            }

            // 检查心跳响应超时
            boolean responseTimeout = (currentTime - lastResponse) > timeoutMs;
            if (responseTimeout) {
                log.info("用户 {} 心跳响应超时，失败次数: {}, 超时阈值: {}ms, 距离上次响应: {}ms",
                         username, heartbeatFailures, timeoutMs, currentTime - lastResponse);
                return true;
            }
        }

        // 检查会话活跃时间超时
        boolean activityTimeout = metadata.isTimeout(timeoutMs);
        if (activityTimeout) {
            long timeSinceLastActivity = currentTime - metadata.getLastActiveTime();
            log.info("用户 {} 会话活跃超时，失败次数: {}, 超时阈值: {}ms, 距离最后活动: {}ms",
                     username, heartbeatFailures, timeoutMs, timeSinceLastActivity);
        } else {
            // 记录会话仍然活跃的信息
            long timeSinceLastActivity = currentTime - metadata.getLastActiveTime();
            log.debug("用户 {} 会话仍然活跃，距离最后活动: {}ms, 超时阈值: {}ms",
                     username, timeSinceLastActivity, timeoutMs);
        }

        return activityTimeout;
    }

    /**
     * 记录用户心跳响应
     */
    public void recordHeartbeatResponse(String username) {
        long currentTime = System.currentTimeMillis();
        Long lastTime = lastHeartbeatResponse.put(username, currentTime);

        if (lastTime != null) {
            long interval = currentTime - lastTime;
            log.info("💗 记录用户 {} 心跳响应，距离上次响应: {}ms", username, interval);
        } else {
            log.info("💗 记录用户 {} 首次心跳响应", username);
        }
    }

    /**
     * 移除用户心跳记录
     */
    public void removeHeartbeatRecord(String username) {
        lastHeartbeatResponse.remove(username);
        log.debug("💔 移除用户 {} 心跳记录", username);
    }

    /**
     * 获取心跳统计信息
     */
    public Map<String, Object> getHeartbeatStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("enabled", heartbeatEnabled);
        stats.put("intervalSeconds", heartbeatIntervalSeconds);
        stats.put("timeoutSeconds", heartbeatTimeoutSeconds);
        stats.put("totalSent", totalHeartbeatsSent.get());
        stats.put("totalSuccess", totalHeartbeatsSuccess.get());
        stats.put("totalFailed", totalHeartbeatsFailed.get());
        stats.put("activeHeartbeatSessions", lastHeartbeatResponse.size());

        // 计算成功率
        long total = totalHeartbeatsSent.get();
        if (total > 0) {
            double successRate = (double) totalHeartbeatsSuccess.get() / total * 100;
            stats.put("successRate", String.format("%.2f%%", successRate));
        } else {
            stats.put("successRate", "0.00%");
        }

        return stats;
    }

    /**
     * 重置心跳统计
     */
    public void resetStats() {
        totalHeartbeatsSent.set(0);
        totalHeartbeatsSuccess.set(0);
        totalHeartbeatsFailed.set(0);
        lastHeartbeatResponse.clear();
        log.info("🔄 心跳统计已重置");
    }
}
