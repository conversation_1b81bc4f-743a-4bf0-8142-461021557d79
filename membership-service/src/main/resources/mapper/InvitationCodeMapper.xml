<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.membership.mapper.InvitationCodeMapper">
    <!-- 基础映射结果集 -->
    <resultMap id="BaseResultMap" type="com.gw.membership.entity.InvitationCodeEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="used_times" jdbcType="INTEGER" property="usedTimes"/>
        <result column="max_use_times" jdbcType="INTEGER" property="maxUseTimes"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>

    <!-- 分页查询邀请码列表 -->
    <select id="page" resultMap="BaseResultMap">
        SELECT * FROM t_invitation_code
        <where>
            deleted = 0
            <if test="code != null and code != ''">
                AND code LIKE CONCAT('%', #{code}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>


    <!-- 根据ID查询邀请码 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT *
        FROM t_invitation_code
        WHERE id = #{id}
          AND deleted = 0
    </select>

    <!-- 根据邀请码查询 -->
    <select id="findByCode" resultMap="BaseResultMap">
        SELECT *
        FROM t_invitation_code
        WHERE code = #{code}
          AND deleted = 0
    </select>

    <!-- 批量查询用户的邀请码 -->
    <select id="findByUsernames" resultMap="BaseResultMap">
        SELECT *
        FROM t_invitation_code
        WHERE username IN
        <foreach collection="usernames" item="username" open="(" separator="," close=")">
            #{username}
        </foreach>
        AND status = 1
        AND deleted = 0
        ORDER BY create_time DESC
    </select>
</mapper>