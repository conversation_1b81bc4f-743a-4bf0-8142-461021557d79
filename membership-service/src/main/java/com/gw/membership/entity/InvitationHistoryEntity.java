package com.gw.membership.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

@Data
@TableName("t_invitation_history")
@NoArgsConstructor
@AllArgsConstructor
public class InvitationHistoryEntity {
    @TableId(type = AUTO)
    private Long id;
    /**
     * 邀请人ID
     */
    private String inviter;
    /**
     * 被邀请人ID
     */
    private String invitee;
    /**
     * 邀请码
     */
    private String invitationCode;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime = LocalDateTime.now();

    public InvitationHistoryEntity(String inviter, String invitee, String invitationCode) {
        this.inviter = inviter;
        this.invitee = invitee;
        this.invitationCode = invitationCode;
    }
}
