package com.gw.membership.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.gw.membership.dto.InvitationCodeDTO;
import com.gw.membership.dto.InvitationRecordQuery;
import com.gw.membership.dto.MyInvitationRecordQuery;
import com.gw.membership.entity.InvitationCodeEntity;
import com.gw.membership.entity.InvitationHistoryEntity;

import java.util.List;
import java.util.Map;

/**
 * 邀请码服务接口
 */
public interface InvitationService {

    /**
     * 创建用户邀请码
     */
    InvitationCodeEntity createInvitationCode(String username);

    /**
     * 获取用户的邀请码
     */
    InvitationCodeEntity getUserInvitationCode(String username);

    /**
     * 验证邀请码是否有效
     */
    boolean validateInvitationCode(String code);

    /**
     * 使用邀请码 - 返回邀请人ID
     */
    void useInvitationCode(String code, String newUsername);

    /**
     * 分页查询邀请码
     */
    IPage<InvitationCodeEntity> pageInvitationCodes(String username, Page<InvitationCodeDTO> page);

    PageInfo<InvitationHistoryEntity> getInvitationRecordPage(int pageNum, int pageSize, InvitationRecordQuery query);

    /**
     * 统计用户的邀请数量
     * @param username 用户名
     * @return 邀请数量
     */
    int countInvitationsByInviter(String username);

    /**
     * 批量获取用户的邀请码
     * @param usernames 用户名列表
     * @return 用户名到邀请码的映射
     */
    Map<String, InvitationCodeEntity> batchGetUserInvitationCodes(List<String> usernames);

    /**
     * 批量统计用户的邀请数量
     * @param usernames 用户名列表
     * @return 用户名到邀请数量的映射
     */
    Map<String, Integer> batchCountInvitationsByInviters(List<String> usernames);

}