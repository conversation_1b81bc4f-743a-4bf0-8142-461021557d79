package com.gw.common.membership.service;

import com.gw.common.membership.vo.InvitationCodeVO;
import com.gw.common.membership.vo.MembershipStatisticsVO;
import com.gw.common.membership.vo.UserInviteStatVO;
import com.gw.common.membership.vo.UserMembershipVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会员服务代理接口
 */
public interface MembershipProxyService {
    UserMembershipVO getMembershipByUsername(String cacheName, String username);

    InvitationCodeVO getInvitationCodeByUsername(String username);

    void useInvitationCode(String username, String code);

    void newUserReg(String username);

    boolean checkBenefitCanUse(String username, String benefitCode);

    void asyncRecordBenefitUsage(String username, String benefitCode, Integer count);

    Map<String, UserInviteStatVO> getInvitationCodeByUsername(List<String> usernames);

    /**
     * 计算指定时间范围内的收款金额
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 收款金额
     */
    double calculateIncomeByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计VIP用户总数
     *
     * @return VIP用户总数
     */
    int countTotalVipUsers();

    /**
     * 计算历史收款总金额
     *
     * @return 历史收款总金额
     */
    double calculateTotalIncome();

    /**
     * 获取会员统计数据
     *
     * @return 会员统计数据VO对象，包含今日收款金额、VIP用户总数、历史收款总金额
     */
    MembershipStatisticsVO getMembershipStatistics();
}
