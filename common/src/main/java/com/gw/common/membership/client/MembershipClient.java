package com.gw.common.membership.client;

import com.gw.common.dto.ResponseResult;
import com.gw.common.dto.TimeRangeDTO;
import com.gw.common.membership.dto.*;
import com.gw.common.membership.vo.InvitationCodeVO;
import com.gw.common.membership.vo.MembershipStatisticsVO;
import com.gw.common.membership.vo.UserMembershipBaseVO;
import com.gw.common.membership.vo.UserMembershipVO;
import com.gw.common.vo.BenefitCanUseVO;
import com.gw.common.vo.BenefitRemainingNumVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 会员服务Feign客户端
 */
@FeignClient(name = "membership-service")
public interface MembershipClient {

    /**
     * 计算指定时间范围内的收款金额
     *
     * @return 收款金额
     */
    @GetMapping("/api/v1/membership/statistics/income")
    ResponseResult<Double> calculateIncomeByTimeRange(@RequestBody TimeRangeDTO req);

    /**
     * 统计VIP用户总数
     *
     * @return VIP用户总数
     */
    @GetMapping("/api/v1/membership/statistics/vip-count")
    ResponseResult<Integer> countTotalVipUsers();

    /**
     * 计算历史收款总金额
     *
     * @return 历史收款总金额
     */
    @GetMapping("/api/v1/membership/statistics/total-income")
    ResponseResult<Double> calculateTotalIncome();

    /**
     * 获取会员统计数据
     *
     * @return 会员统计数据VO
     */
    @GetMapping("/api/v1/membership/statistics/summary")
    ResponseResult<MembershipStatisticsVO> getMembershipStatistics();

    @PostMapping("/api/v1/membership/internal/query_by_username")
    ResponseResult<UserMembershipVO> getMembershipByUsername(@RequestBody MembershipQueryDTO req);

    @PostMapping("/api/v1/membership/invitation/code/code_by_username")
    ResponseResult<InvitationCodeVO> getInvitationCodeByUsername(@RequestBody InvitationCodeQuery req);

    @PostMapping("/api/v1/membership/invitation/code/use")
    ResponseResult<?> useInvitationCode(@RequestBody InvitationCodeUseDTO req);

    @PostMapping("/api/v1/membership/new_user_reg")
    ResponseResult<?> newUserReg(@RequestBody NewUserRegisterDTO req);

    @PostMapping("/api/v1/membership/internal/query_base_by_username")
    ResponseResult<UserMembershipBaseVO> getMembershipBaseContentByUsername(@RequestBody MembershipQueryDTO req);

    @PostMapping("/api/v1/membership/admin/benefit/query_can_use")
    ResponseResult<BenefitCanUseVO> queryCanUse(@RequestBody BenefitCanUseQueryDTO req);

    @PostMapping("/api/v1/membership/admin/benefit/usage_record")
    ResponseResult<BenefitRemainingNumVO> usageRecord(@RequestBody BenefitUsageRecordDTO req);
    @PostMapping("/api/v1/membership/invitation/code/code_by_username")
    ResponseResult<InvitationCodeVO> getInvitationCodeByUsername(@RequestBody InvitationCodeQuery req);
}
