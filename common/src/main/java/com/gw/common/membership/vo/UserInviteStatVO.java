package com.gw.common.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户邀请统计VO")
public class UserInviteStatVO {
    @Schema(description = "邀请数量")
    private Integer inviteCount;

    @Schema(description = "邀请码")
    private String inviteCode;

    @Schema(description = "邀请人")
    private String invitor;
    private UserMembershipVO membership;
}
