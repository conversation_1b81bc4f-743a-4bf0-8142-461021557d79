package com.gw.common.agent.service;

import com.gw.common.agent.client.AgentClient;
import com.gw.common.agent.dto.SensitiveWordCheckDTO;
import com.gw.common.agent.dto.SensitiveWordCheckResultVO;
import com.gw.common.dto.ResponseResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Log4j2
public class SensitiveWordDetectProxyService {
    private final AgentClient agentClient;
    public Boolean checkContent(String content) {
        if(content == null || content.trim().isEmpty()){
            return true;
        }
        SensitiveWordCheckDTO  req = new SensitiveWordCheckDTO();
        req.setContent(content);
        var res = agentClient.checkText(req).getData();
        ResponseResult<SensitiveWordCheckResultVO> rsp = agentClient.checkText(req);

        if (rsp.getCode() == 200 && rsp.getData() != null) {

            return !rsp.getData().getHasSensitiveWord();
        } else {
            log.error("内容审核调用出错，content: {}, error: {}", content, rsp.getMsg());
            return true;
        }
    }
}
